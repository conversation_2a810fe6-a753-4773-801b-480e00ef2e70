#!/usr/bin/env python3
"""
Modern CLI interface for the reCAPTCHA solver.
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Optional

# Add the package to path for development
sys.path.insert(0, str(Path(__file__).parent))

from recaptcha_solver import RecaptchaSolver, SolverConfig
from recaptcha_solver.config import BrowserConfig, ModelConfig, DetectionConfig
from recaptcha_solver.exceptions import RecaptchaSolverError


def create_parser() -> argparse.ArgumentParser:
    """Create the command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Professional reCAPTCHA Solver",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s https://www.google.com/recaptcha/api2/demo
  %(prog)s --browser firefox --proxy 127.0.0.1:8080 https://example.com
  %(prog)s --config config.yaml https://example.com
  %(prog)s --test-config
        """
    )
    
    # Main arguments
    parser.add_argument(
        'url',
        nargs='?',
        help='URL containing reCAPTCHA challenge'
    )
    
    # Configuration
    parser.add_argument(
        '--config', '-c',
        type=Path,
        help='Configuration file (YAML or JSON)'
    )
    
    parser.add_argument(
        '--save-config',
        type=Path,
        help='Save current configuration to file'
    )
    
    # Browser settings
    browser_group = parser.add_argument_group('Browser Settings')
    browser_group.add_argument(
        '--browser', '-b',
        choices=['chrome', 'firefox', 'edge'],
        default='chrome',
        help='Browser to use (default: chrome)'
    )
    
    browser_group.add_argument(
        '--headless',
        action='store_true',
        help='Run browser in headless mode'
    )
    
    browser_group.add_argument(
        '--browser-binary',
        type=Path,
        help='Path to browser binary'
    )
    
    browser_group.add_argument(
        '--proxy',
        help='Proxy server (host:port)'
    )
    
    browser_group.add_argument(
        '--user-agent',
        help='Custom user agent string'
    )
    
    # Model settings
    model_group = parser.add_argument_group('Model Settings')
    model_group.add_argument(
        '--model-path',
        type=Path,
        default=Path('./models/models.onnx'),
        help='Path to YOLO model file'
    )
    
    model_group.add_argument(
        '--confidence-threshold',
        type=float,
        default=0.25,
        help='Detection confidence threshold (0.0-1.0)'
    )
    
    # Output settings
    output_group = parser.add_argument_group('Output Settings')
    output_group.add_argument(
        '--output', '-o',
        choices=['json', 'yaml', 'text'],
        default='text',
        help='Output format (default: text)'
    )
    
    output_group.add_argument(
        '--output-file',
        type=Path,
        help='Save results to file'
    )
    
    output_group.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    output_group.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='Suppress output except errors'
    )
    
    # Utility commands
    util_group = parser.add_argument_group('Utility Commands')
    util_group.add_argument(
        '--test-config',
        action='store_true',
        help='Test configuration and exit'
    )
    
    util_group.add_argument(
        '--list-browsers',
        action='store_true',
        help='List supported browsers and exit'
    )
    
    util_group.add_argument(
        '--version',
        action='store_true',
        help='Show version information and exit'
    )
    
    return parser


def load_config(args) -> SolverConfig:
    """Load configuration from file or create from arguments."""
    if args.config:
        print(f"Loading configuration from: {args.config}")
        config = SolverConfig.from_file(args.config)
    else:
        # Create config from command line arguments
        browser_config = BrowserConfig(
            browser_type=args.browser,
            headless=args.headless,
            binary_path=args.browser_binary,
            user_agent=args.user_agent
        )
        
        # Parse proxy if provided
        if args.proxy:
            if ':' in args.proxy:
                host, port = args.proxy.split(':', 1)
                browser_config.proxy_host = host
                browser_config.proxy_port = int(port)
            else:
                print(f"Warning: Invalid proxy format '{args.proxy}', expected host:port")
        
        model_config = ModelConfig(
            primary_model_path=args.model_path,
            confidence_threshold=args.confidence_threshold
        )
        
        detection_config = DetectionConfig()
        
        config = SolverConfig(
            browser=browser_config,
            model=model_config,
            detection=detection_config
        )
        
        # Set log level based on verbosity
        if args.verbose:
            config.log_level = "DEBUG"
        elif args.quiet:
            config.log_level = "ERROR"
    
    return config


def format_output(result, format_type: str) -> str:
    """Format the result for output."""
    if format_type == 'json':
        return json.dumps(result.to_dict(), indent=2)
    elif format_type == 'yaml':
        try:
            import yaml
            return yaml.dump(result.to_dict(), default_flow_style=False)
        except ImportError:
            print("Warning: PyYAML not installed, falling back to JSON")
            return json.dumps(result.to_dict(), indent=2)
    else:  # text format
        lines = []
        lines.append("=" * 60)
        lines.append("reCAPTCHA Solver Results")
        lines.append("=" * 60)
        
        if result.success:
            lines.append(f"✓ Status: SUCCESS")
            lines.append(f"✓ Token: {result.recaptcha_token[:20]}...{result.recaptcha_token[-20:] if result.recaptcha_token and len(result.recaptcha_token) > 40 else result.recaptcha_token}")
        else:
            lines.append(f"✗ Status: FAILED")
            if result.error:
                lines.append(f"✗ Error: {result.error}")
        
        lines.append(f"⏱ Total Time: {result.total_time:.2f} seconds")
        lines.append(f"🔄 Attempts: {result.total_attempts}")
        
        if result.challenges:
            lines.append(f"📊 Success Rate: {result.get_success_rate():.1%}")
        
        lines.append("=" * 60)
        return "\n".join(lines)


def main():
    """Main CLI function."""
    parser = create_parser()
    args = parser.parse_args()
    
    # Handle utility commands
    if args.version:
        from recaptcha_solver import __version__
        print(f"reCAPTCHA Solver v{__version__}")
        return 0
    
    if args.list_browsers:
        solver = RecaptchaSolver()
        browsers = solver.get_supported_browsers()
        print("Supported browsers:")
        for browser in browsers:
            print(f"  - {browser}")
        return 0
    
    if args.test_config:
        try:
            config = load_config(args)
            solver = RecaptchaSolver(config)
            test_results = solver.test_configuration()
            
            print("Configuration Test Results:")
            print(f"  Config Valid: {'✓' if test_results['config_valid'] else '✗'}")
            print(f"  Browser Available: {'✓' if test_results['browser_available'] else '✗'}")
            print(f"  Model Available: {'✓' if test_results['model_available'] else '✗'}")
            
            if test_results['errors']:
                print("\nErrors:")
                for error in test_results['errors']:
                    print(f"  - {error}")
                return 1
            else:
                print("\n✓ All tests passed!")
                return 0
        
        except Exception as e:
            print(f"Configuration test failed: {e}")
            return 1
    
    # Require URL for solving
    if not args.url:
        parser.error("URL is required for solving reCAPTCHA")
    
    try:
        # Load configuration
        config = load_config(args)
        
        # Save configuration if requested
        if args.save_config:
            config.save_to_file(args.save_config)
            print(f"Configuration saved to: {args.save_config}")
        
        # Create solver and solve
        with RecaptchaSolver(config) as solver:
            if not args.quiet:
                print(f"Solving reCAPTCHA at: {args.url}")
            
            result = solver.solve(args.url)
            
            # Format and output results
            output = format_output(result, args.output)
            
            if args.output_file:
                with open(args.output_file, 'w') as f:
                    f.write(output)
                if not args.quiet:
                    print(f"Results saved to: {args.output_file}")
            else:
                print(output)
            
            return 0 if result.success else 1
    
    except RecaptchaSolverError as e:
        print(f"Solver error: {e}")
        return 1
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 130
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
