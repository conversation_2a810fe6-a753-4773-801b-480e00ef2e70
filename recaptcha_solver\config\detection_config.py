"""
Detection configuration settings.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional
from .base import BaseConfig, ConfigValidator


@dataclass
class DetectionConfig(BaseConfig):
    """Configuration for detection settings."""
    
    # Grid settings
    default_grid_size: int = 3
    supported_grid_sizes: List[int] = field(default_factory=lambda: [3, 4])
    
    # Detection thresholds
    min_confidence: float = 0.25
    iou_threshold: float = 0.1
    overlap_threshold: float = 0.3
    
    # Image processing
    enhance_contrast: bool = True
    apply_noise_reduction: bool = True
    resize_images: bool = False
    target_image_size: tuple = (640, 640)
    
    # Retry settings
    max_detection_attempts: int = 3
    retry_on_no_detection: bool = True
    retry_delay: float = 0.5
    
    # Target class mappings
    target_mappings: Dict[str, int] = field(default_factory=lambda: {
        # Vehicles
        "bicycle": 1,
        "bus": 5,
        "boat": 8,
        "car": 2,
        "motorcycle": 3,
        "truck": 7,
        "airplane": 4,
        "train": 6,
        
        # Traffic elements
        "traffic": 9,
        "traffic light": 9,
        "stop sign": 11,
        "hydrant": 10,
        "fire hydrant": 10,
        
        # Infrastructure
        "crosswalk": 12,
        "bridge": 21,
        "stairs": 24,
        "chimney": 25,
        
        # Nature
        "palm tree": 22,
        "mountain": 23,
        "tree": 22,
        
        # Animals
        "person": 0,
        "bird": 14,
        "cat": 15,
        "dog": 16,
        "horse": 17,
        "sheep": 18,
        "cow": 19,
        "elephant": 20,
        
        # Objects
        "bench": 13,
        "tractor": 26,
    })
    
    # Alternative target names (synonyms)
    target_synonyms: Dict[str, str] = field(default_factory=lambda: {
        "cars": "car",
        "vehicles": "car",
        "automobiles": "car",
        "buses": "bus",
        "trucks": "truck",
        "motorcycles": "motorcycle",
        "bikes": "bicycle",
        "bicycles": "bicycle",
        "boats": "boat",
        "ships": "boat",
        "planes": "airplane",
        "aircraft": "airplane",
        "trains": "train",
        "traffic lights": "traffic light",
        "stop signs": "stop sign",
        "fire hydrants": "fire hydrant",
        "crosswalks": "crosswalk",
        "bridges": "bridge",
        "mountains": "mountain",
        "trees": "tree",
        "palm trees": "palm tree",
        "people": "person",
        "persons": "person",
        "humans": "person",
        "birds": "bird",
        "cats": "cat",
        "dogs": "dog",
        "horses": "horse",
        "cows": "cow",
        "elephants": "elephant",
    })
    
    # Timeout settings
    detection_timeout: float = 30.0
    image_download_timeout: float = 15.0
    
    # Performance settings
    parallel_processing: bool = False
    max_workers: int = 4
    
    def validate(self) -> None:
        """Validate detection configuration."""
        # Validate grid settings
        ConfigValidator.validate_range(
            self.default_grid_size, 2, 6, "default_grid_size"
        )
        
        for i, size in enumerate(self.supported_grid_sizes):
            ConfigValidator.validate_range(
                size, 2, 6, f"supported_grid_sizes[{i}]"
            )
        
        # Validate thresholds
        ConfigValidator.validate_range(
            self.min_confidence, 0.0, 1.0, "min_confidence"
        )
        ConfigValidator.validate_range(
            self.iou_threshold, 0.0, 1.0, "iou_threshold"
        )
        ConfigValidator.validate_range(
            self.overlap_threshold, 0.0, 1.0, "overlap_threshold"
        )
        
        # Validate retry settings
        ConfigValidator.validate_range(
            self.max_detection_attempts, 1, 10, "max_detection_attempts"
        )
        ConfigValidator.validate_range(
            self.retry_delay, 0.0, 10.0, "retry_delay"
        )
        
        # Validate timeouts
        ConfigValidator.validate_range(
            self.detection_timeout, 1.0, 300.0, "detection_timeout"
        )
        ConfigValidator.validate_range(
            self.image_download_timeout, 1.0, 60.0, "image_download_timeout"
        )
        
        # Validate performance settings
        ConfigValidator.validate_range(
            self.max_workers, 1, 16, "max_workers"
        )
        
        # Validate target image size
        if not isinstance(self.target_image_size, tuple) or len(self.target_image_size) != 2:
            from ..exceptions.config import InvalidConfigError
            raise InvalidConfigError(
                config_key="target_image_size",
                config_value=self.target_image_size,
                expected_type="tuple of 2 integers"
            )
    
    def get_target_class(self, target_text: str) -> Optional[int]:
        """Get target class ID from text description."""
        # Normalize text
        normalized_text = target_text.lower().strip()
        
        # Check direct mapping
        if normalized_text in self.target_mappings:
            return self.target_mappings[normalized_text]
        
        # Check synonyms
        if normalized_text in self.target_synonyms:
            canonical_name = self.target_synonyms[normalized_text]
            return self.target_mappings.get(canonical_name)
        
        # Check partial matches
        for key, value in self.target_mappings.items():
            if key in normalized_text or normalized_text in key:
                return value
        
        return None
    
    def get_supported_targets(self) -> List[str]:
        """Get list of all supported target names."""
        targets = list(self.target_mappings.keys())
        targets.extend(self.target_synonyms.keys())
        return sorted(set(targets))
