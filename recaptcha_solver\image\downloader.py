"""
Image downloader with retry logic and validation.
"""

import logging
import hashlib
from pathlib import Path
from typing import Optional, Dict, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from ..exceptions.image import ImageDownloadError
from ..config.detection_config import DetectionConfig


class ImageDownloader:
    """Handles downloading images from URLs with retry logic."""
    
    def __init__(self, config: DetectionConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """Create a requests session with retry strategy."""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set headers
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        return session
    
    def download_image(self, url: str, output_path: Path, validate: bool = True) -> bool:
        """
        Download an image from URL to the specified path.
        
        Args:
            url: URL of the image to download
            output_path: Path where to save the image
            validate: Whether to validate the downloaded image
            
        Returns:
            True if download was successful, False otherwise
            
        Raises:
            ImageDownloadError: If download fails after retries
        """
        if not url or not url.strip():
            raise ImageDownloadError(
                url=url,
                reason="Empty or invalid URL"
            )
        
        try:
            self.logger.debug(f"Downloading image from: {url}")
            
            response = self.session.get(
                url,
                timeout=self.config.image_download_timeout,
                verify=False,
                stream=True
            )
            response.raise_for_status()
            
            # Validate content type
            content_type = response.headers.get('content-type', '').lower()
            if not self._is_valid_image_content_type(content_type):
                self.logger.warning(f"Unexpected content type: {content_type}")
            
            # Create output directory if it doesn't exist
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Download and save the image
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            # Verify file was created and has content
            if not output_path.exists() or output_path.stat().st_size == 0:
                raise ImageDownloadError(
                    url=url,
                    reason="Downloaded file is empty or doesn't exist"
                )
            
            # Validate image if requested
            if validate:
                from .validator import ImageValidator
                validator = ImageValidator()
                if not validator.validate_image_file(output_path):
                    output_path.unlink(missing_ok=True)
                    raise ImageDownloadError(
                        url=url,
                        reason="Downloaded file is not a valid image"
                    )
            
            self.logger.debug(f"Successfully downloaded image to: {output_path}")
            return True
        
        except requests.exceptions.RequestException as e:
            raise ImageDownloadError(
                url=url,
                status_code=getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None,
                reason=str(e),
                cause=e
            )
        except Exception as e:
            raise ImageDownloadError(
                url=url,
                reason=f"Unexpected error: {str(e)}",
                cause=e
            )
    
    def download_multiple_images(self, url_path_pairs: list) -> Dict[str, bool]:
        """
        Download multiple images.
        
        Args:
            url_path_pairs: List of (url, path) tuples
            
        Returns:
            Dictionary mapping URLs to success status
        """
        results = {}
        
        for url, path in url_path_pairs:
            try:
                success = self.download_image(url, path)
                results[url] = success
            except Exception as e:
                self.logger.error(f"Failed to download {url}: {e}")
                results[url] = False
        
        return results
    
    def get_image_info(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Get information about an image without downloading it.
        
        Args:
            url: URL of the image
            
        Returns:
            Dictionary with image information or None if failed
        """
        try:
            response = self.session.head(url, timeout=10)
            response.raise_for_status()
            
            return {
                'url': url,
                'content_type': response.headers.get('content-type'),
                'content_length': response.headers.get('content-length'),
                'last_modified': response.headers.get('last-modified'),
                'etag': response.headers.get('etag'),
            }
        
        except Exception as e:
            self.logger.warning(f"Failed to get image info for {url}: {e}")
            return None
    
    def _is_valid_image_content_type(self, content_type: str) -> bool:
        """Check if content type indicates an image."""
        valid_types = [
            'image/',
            'application/octet-stream'  # Sometimes images are served with this type
        ]
        return any(img_type in content_type for img_type in valid_types)
    
    def calculate_url_hash(self, url: str) -> str:
        """Calculate a hash for the URL for caching purposes."""
        return hashlib.md5(url.encode()).hexdigest()
    
    def close(self) -> None:
        """Close the session."""
        if self.session:
            self.session.close()
