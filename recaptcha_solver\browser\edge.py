"""
Edge browser implementation.
"""

from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.edge.options import Options as EdgeOptions
from seleniumwire import webdriver

from .base import BaseBrowser


class EdgeBrowser(BaseBrowser):
    """Edge browser implementation."""
    
    def get_browser_name(self) -> str:
        """Get the browser name."""
        return "Edge"
    
    def _create_driver(self) -> webdriver.Edge:
        """Create Edge driver with configuration."""
        options = self._create_edge_options()
        service = self._create_edge_service()
        seleniumwire_options = self._create_seleniumwire_options()
        
        return webdriver.Edge(
            service=service,
            options=options,
            seleniumwire_options=seleniumwire_options
        )
    
    def _create_edge_options(self) -> EdgeOptions:
        """Create Edge options based on configuration."""
        options = EdgeOptions()
        
        # Set binary path if specified
        if self.config.binary_path:
            options.binary_location = str(self.config.binary_path)
        
        # Basic options
        if self.config.headless:
            options.add_argument("--headless")
        
        # Security options
        if self.config.ignore_ssl_errors:
            options.add_argument("--ignore-certificate-errors")
            options.add_argument("--ignore-ssl-errors")
            options.add_argument("--allow-insecure-localhost")
        
        if self.config.disable_web_security:
            options.add_argument("--disable-web-security")
        
        # Performance options
        if self.config.disable_images:
            prefs = {"profile.managed_default_content_settings.images": 2}
            options.add_experimental_option("prefs", prefs)
        
        # Window size
        if not self.config.headless:
            options.add_argument(f"--window-size={self.config.window_width},{self.config.window_height}")
        
        # User agent
        user_agent = self.config.get_effective_user_agent()
        options.add_argument(f"--user-agent={user_agent}")
        
        # Add custom Edge options
        for option in self.config.edge_options:
            options.add_argument(option)
        
        # Additional stability options
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-gpu")
        options.add_argument("--lang=en-US")
        
        return options
    
    def _create_edge_service(self) -> EdgeService:
        """Create Edge service."""
        return EdgeService()
    
    def _create_seleniumwire_options(self) -> dict:
        """Create selenium-wire options."""
        seleniumwire_options = {
            'verify_ssl': not self.config.ignore_ssl_errors,
            'disable_encoding': True,
            'suppress_connection_errors': True,
            'request_storage_base_dir': None  # Use memory storage
        }
        
        # Add proxy if configured
        if self.config.proxy_host and self.config.proxy_port:
            proxy_url = self.config.proxy_url
            seleniumwire_options['proxy'] = {
                'http': proxy_url,
                'https': proxy_url,
                'no_proxy': 'localhost,127.0.0.1'
            }
        
        return seleniumwire_options
