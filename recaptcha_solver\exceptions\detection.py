"""
Detection-related exception classes.
"""

from typing import Optional
from .base import RecaptchaSolverError, RetryableError, NonRetryableError


class DetectionError(RecaptchaSolverError):
    """Base class for detection-related errors."""
    pass


class NoDetectionError(DetectionError, RetryableError):
    """Raised when no objects are detected in the image."""
    
    def __init__(
        self, 
        target_class: Optional[str] = None,
        confidence_threshold: Optional[float] = None,
        **kwargs
    ):
        message = "No objects detected in image"
        if target_class:
            message += f" for target class: {target_class}"
        if confidence_threshold:
            message += f" (confidence threshold: {confidence_threshold})"
        
        super().__init__(
            message,
            details={
                "target_class": target_class,
                "confidence_threshold": confidence_threshold
            },
            **kwargs
        )


class InvalidDetectionError(DetectionError, NonRetryableError):
    """Raised when detection results are invalid."""
    
    def __init__(
        self, 
        reason: str,
        detection_data: Optional[dict] = None,
        **kwargs
    ):
        message = f"Invalid detection results: {reason}"
        super().__init__(
            message,
            details={
                "reason": reason,
                "detection_data": detection_data
            },
            **kwargs
        )


class DetectionTimeoutError(DetectionError, RetryableError):
    """Raised when detection process times out."""
    
    def __init__(
        self, 
        timeout: float,
        **kwargs
    ):
        message = f"Detection process timed out after {timeout} seconds"
        super().__init__(
            message,
            details={"timeout": timeout},
            **kwargs
        )


class UnsupportedTargetError(DetectionError, NonRetryableError):
    """Raised when target object type is not supported."""
    
    def __init__(
        self, 
        target_type: str, 
        supported_types: Optional[list] = None,
        **kwargs
    ):
        message = f"Unsupported target type: {target_type}"
        if supported_types:
            message += f". Supported types: {', '.join(supported_types)}"
        
        super().__init__(
            message,
            details={
                "target_type": target_type,
                "supported_types": supported_types or []
            },
            **kwargs
        )
