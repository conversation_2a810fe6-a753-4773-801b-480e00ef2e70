"""
Browser manager for handling browser lifecycle and operations.
"""

import logging
from typing import Optional, List, Dict, Any
from contextlib import contextmanager

from ..config.browser_config import BrowserConfig
from ..exceptions.browser import <PERSON><PERSON>erError
from .factory import BrowserFactory
from .base import BaseBrowser


class BrowserManager:
    """Manager class for browser operations and lifecycle."""
    
    def __init__(self, config: BrowserConfig):
        self.config = config
        self.browser: Optional[BaseBrowser] = None
        self.logger = logging.getLogger(__name__)
        self._session_cookies: List[Dict[str, Any]] = []
    
    def initialize_browser(self) -> BaseBrowser:
        """Initialize and return a browser instance."""
        if self.browser and self.browser.is_initialized:
            return self.browser
        
        try:
            self.browser = BrowserFactory.create_browser(self.config)
            self.browser.initialize()
            
            # Restore session cookies if any
            if self._session_cookies:
                self._restore_cookies()
            
            return self.browser
        
        except Exception as e:
            self.logger.error(f"Failed to initialize browser: {e}")
            raise
    
    def get_browser(self) -> BaseBrowser:
        """Get the current browser instance, initializing if necessary."""
        if not self.browser or not self.browser.is_initialized:
            return self.initialize_browser()
        return self.browser
    
    def close_browser(self) -> None:
        """Close the current browser instance."""
        if self.browser:
            try:
                # Save cookies before closing
                self._save_cookies()
                self.browser.close()
            except Exception as e:
                self.logger.warning(f"Error closing browser: {e}")
            finally:
                self.browser = None
    
    def restart_browser(self) -> BaseBrowser:
        """Restart the browser (close and reinitialize)."""
        self.close_browser()
        return self.initialize_browser()
    
    def navigate_to(self, url: str) -> None:
        """Navigate to a URL using the managed browser."""
        browser = self.get_browser()
        browser.navigate_to(url)
    
    def add_cookies(self, cookies: List[Dict[str, Any]]) -> None:
        """Add cookies to the browser session."""
        browser = self.get_browser()
        
        for cookie in cookies:
            try:
                browser.add_cookie(cookie)
                self.logger.debug(f"Added cookie: {cookie.get('name', 'unknown')}")
            except Exception as e:
                self.logger.warning(f"Failed to add cookie {cookie}: {e}")
    
    def get_cookies(self) -> List[Dict[str, Any]]:
        """Get all cookies from the browser."""
        if not self.browser or not self.browser.is_initialized:
            return []
        
        try:
            return self.browser.get_cookies()
        except Exception as e:
            self.logger.warning(f"Failed to get cookies: {e}")
            return []
    
    def _save_cookies(self) -> None:
        """Save current session cookies."""
        try:
            self._session_cookies = self.get_cookies()
            self.logger.debug(f"Saved {len(self._session_cookies)} cookies")
        except Exception as e:
            self.logger.warning(f"Failed to save cookies: {e}")
    
    def _restore_cookies(self) -> None:
        """Restore saved session cookies."""
        if not self._session_cookies or not self.browser:
            return
        
        try:
            for cookie in self._session_cookies:
                self.browser.add_cookie(cookie)
            self.logger.debug(f"Restored {len(self._session_cookies)} cookies")
        except Exception as e:
            self.logger.warning(f"Failed to restore cookies: {e}")
    
    def execute_with_retry(self, operation, max_retries: int = 3, *args, **kwargs):
        """Execute an operation with browser restart on failure."""
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                browser = self.get_browser()
                return operation(browser, *args, **kwargs)
            
            except Exception as e:
                last_exception = e
                self.logger.warning(f"Browser operation failed (attempt {attempt + 1}/{max_retries}): {e}")
                
                if attempt < max_retries - 1:
                    self.logger.info("Restarting browser for retry...")
                    try:
                        self.restart_browser()
                    except Exception as restart_error:
                        self.logger.error(f"Failed to restart browser: {restart_error}")
        
        # If all retries failed, raise the last exception
        raise BrowserError(
            f"Operation failed after {max_retries} attempts",
            cause=last_exception
        )
    
    @contextmanager
    def browser_context(self):
        """Context manager for browser operations."""
        try:
            browser = self.initialize_browser()
            yield browser
        finally:
            self.close_browser()
    
    def is_browser_alive(self) -> bool:
        """Check if the browser is alive and responsive."""
        if not self.browser or not self.browser.is_initialized:
            return False
        
        try:
            # Try to get current URL as a simple health check
            self.browser.get_current_url()
            return True
        except Exception:
            return False
    
    def get_browser_info(self) -> Dict[str, Any]:
        """Get information about the current browser."""
        if not self.browser:
            return {"status": "not_initialized"}
        
        return {
            "status": "initialized" if self.browser.is_initialized else "not_initialized",
            "browser_type": self.browser.get_browser_name(),
            "current_url": self.browser.get_current_url() if self.browser.is_initialized else None,
            "cookies_count": len(self.get_cookies()),
        }
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close_browser()
