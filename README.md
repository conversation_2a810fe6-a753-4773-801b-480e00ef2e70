# Professional reCAPTCHA Solver

A professional-grade reCAPTCHA solver built with modern software engineering practices. This solution uses advanced computer vision, machine learning, and browser automation to solve image-based reCAPTCHA challenges with high accuracy and reliability.

## 🚀 Features

### Core Capabilities
- **Multi-browser support**: Chrome, Firefox, and Edge with automatic binary detection
- **Advanced object detection**: YOLO-based detection with confidence scoring and IoU analysis
- **Dynamic challenge handling**: Supports static, dynamic, and multi-step reCAPTCHA challenges
- **Intelligent image processing**: Preprocessing, enhancement, and validation pipeline
- **Professional architecture**: Modular, extensible, and maintainable codebase

### Enterprise Features
- **Robust configuration management**: YAML/JSON configs with validation and environment variables
- **Comprehensive error handling**: Custom exceptions with retry mechanisms and circuit breakers
- **Performance monitoring**: Built-in analytics, timing metrics, and success rate tracking
- **Flexible deployment**: CLI interface, Python API, and future REST API support
- **Production ready**: Logging, caching, resource management, and cleanup

## 📋 Requirements

- **Python**: 3.8 or higher
- **Operating System**: Windows, macOS, or Linux
- **Browser**: Chrome, Firefox, or Edge (automatically detected)
- **Memory**: Minimum 4GB RAM recommended
- **Storage**: 500MB for models and dependencies

## 🛠 Installation

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd Recaptcha-Image

# Install dependencies
pip install -r requirements.txt

# Test the installation
python cli.py --test-config
```

### Development Installation
```bash
# Install with development dependencies
pip install -r requirements.txt

# Install pre-commit hooks (optional)
pre-commit install
```

## 🚀 Usage

### Command Line Interface

#### Basic Usage
```bash
# Solve reCAPTCHA on Google's demo page
python cli.py https://www.google.com/recaptcha/api2/demo

# Use a specific browser
python cli.py --browser firefox https://example.com

# Run in headless mode with proxy
python cli.py --headless --proxy 127.0.0.1:8080 https://example.com
```

#### Advanced Configuration
```bash
# Use custom configuration file
python cli.py --config config.yaml https://example.com

# Save current configuration
python cli.py --save-config my-config.yaml --browser chrome --headless

# Test configuration without solving
python cli.py --test-config

# Get detailed output in JSON format
python cli.py --output json --output-file results.json https://example.com
```

#### Configuration File Example
```yaml
# config.yaml
browser:
  browser_type: chrome
  headless: false
  window_width: 1280
  window_height: 1024
  proxy_host: 127.0.0.1
  proxy_port: 8080

model:
  primary_model_path: ./models/models.onnx
  confidence_threshold: 0.25
  iou_threshold: 0.1

detection:
  enhance_contrast: true
  max_detection_attempts: 3
  retry_on_no_detection: true

log_level: INFO
max_solve_attempts: 10
```

### Python API Usage

```python
from recaptcha_solver import RecaptchaSolver, SolverConfig
from recaptcha_solver.config import BrowserConfig, ModelConfig

# Create custom configuration
config = SolverConfig(
    browser=BrowserConfig(
        browser_type="chrome",
        headless=True,
        proxy_host="127.0.0.1",
        proxy_port=8080
    ),
    model=ModelConfig(
        confidence_threshold=0.3
    )
)

# Solve reCAPTCHA
with RecaptchaSolver(config) as solver:
    result = solver.solve("https://example.com/recaptcha")

    if result.success:
        print(f"Token: {result.recaptcha_token}")
        print(f"Time: {result.total_time:.2f}s")
    else:
        print(f"Failed: {result.error}")
```

## 🏗 Architecture

### Project Structure
```
recaptcha_solver/
├── __init__.py                   # Package initialization
├── core/                         # Core solver components
│   ├── solver.py                # Main solver class
│   ├── result.py                # Result classes
│   └── session.py               # Session management
├── browser/                      # Browser management
│   ├── manager.py               # Browser lifecycle manager
│   ├── factory.py               # Browser factory
│   ├── base.py                  # Base browser class
│   ├── chrome.py                # Chrome implementation
│   ├── firefox.py               # Firefox implementation
│   └── edge.py                  # Edge implementation
├── config/                       # Configuration system
│   ├── settings.py              # Main configuration
│   ├── browser_config.py        # Browser settings
│   ├── model_config.py          # Model settings
│   └── detection_config.py      # Detection settings
├── image/                        # Image processing
│   ├── processor.py             # Main image processor
│   ├── downloader.py            # Image downloader
│   ├── validator.py             # Image validation
│   ├── enhancer.py              # Image enhancement
│   └── cache.py                 # Image caching
├── models/                       # Model management
│   ├── manager.py               # Model lifecycle
│   ├── loader.py                # Model loading
│   └── predictor.py             # Prediction engine
├── detection/                    # Detection engine
│   ├── engine.py                # Main detection engine
│   ├── strategies.py            # Detection strategies
│   └── grid.py                  # Grid analysis
├── exceptions/                   # Exception classes
│   ├── base.py                  # Base exceptions
│   ├── browser.py               # Browser exceptions
│   ├── model.py                 # Model exceptions
│   └── ...                      # Other exceptions
└── utils/                        # Utility functions
    ├── logging.py               # Logging utilities
    ├── timing.py                # Timing utilities
    └── helpers.py               # General helpers
```

### Key Components

1. **RecaptchaSolver**: Main orchestrator that coordinates all components
2. **BrowserManager**: Handles browser lifecycle, sessions, and error recovery
3. **ImageProcessor**: Downloads, validates, enhances, and caches images
4. **ModelManager**: Loads and manages YOLO models with fallback support
5. **DetectionEngine**: Performs object detection and grid analysis
6. **ConfigSystem**: Manages configuration with validation and environment support

## 🔧 How It Works

### Solving Process
1. **Initialization**: Load configuration, initialize browser and models
2. **Navigation**: Navigate to target URL and inject cookies if provided
3. **reCAPTCHA Detection**: Locate and interact with reCAPTCHA elements
4. **Challenge Analysis**: Determine challenge type (3x3, 4x4, dynamic)
5. **Image Processing**: Download and enhance challenge images
6. **Object Detection**: Use YOLO model to detect target objects
7. **Grid Analysis**: Calculate IoU overlap to determine grid selections
8. **Answer Submission**: Click appropriate grid cells and verify
9. **Token Extraction**: Extract reCAPTCHA token from network traffic
10. **Result Compilation**: Return comprehensive results with metrics

### Supported Challenge Types
- **Static 3x3 Grids**: Standard 9-cell image selection
- **Static 4x4 Grids**: 16-cell image selection
- **Dynamic Challenges**: Multi-step challenges with image replacement
- **Square Detection**: Various object categories with high accuracy

### Detection Capabilities
The solver can detect 25+ object classes including:

**Vehicles**: cars, buses, trucks, motorcycles, bicycles, boats, airplanes, trains
**Traffic Elements**: traffic lights, stop signs, fire hydrants, crosswalks
**Infrastructure**: bridges, stairs, chimneys, benches
**Nature**: trees, palm trees, mountains
**Animals**: people, birds, cats, dogs, horses, cows, elephants, sheep

## 🔧 Configuration

### Environment Variables
```bash
# Browser settings
RECAPTCHA_BROWSER_TYPE=chrome
RECAPTCHA_BROWSER_HEADLESS=true
RECAPTCHA_PROXY_HOST=127.0.0.1
RECAPTCHA_PROXY_PORT=8080

# Model settings
RECAPTCHA_MODEL_PATH=./models/models.onnx
RECAPTCHA_CONFIDENCE_THRESHOLD=0.25

# Logging
RECAPTCHA_LOG_LEVEL=INFO
RECAPTCHA_LOG_FILE=./logs/solver.log
```

### Configuration Profiles
```python
# Development profile
config = SolverConfig.create_for_development()

# Production profile
config = SolverConfig.create_for_production()

# Testing profile
config = SolverConfig.create_for_testing()
```

## 🐛 Troubleshooting

### Common Issues

| Issue | Solution |
|-------|----------|
| **Browser not found** | Use `--browser-binary` or install browser |
| **Model file missing** | Ensure `models/models.onnx` exists |
| **SSL certificate errors** | Use `--ignore-ssl-errors` or check network |
| **Memory issues** | Reduce `max_workers` or use headless mode |
| **Proxy connection failed** | Verify proxy settings and connectivity |

### Diagnostic Commands
```bash
# Test configuration
python cli.py --test-config

# List supported browsers
python cli.py --list-browsers

# Verbose debugging
python cli.py --verbose --output json https://example.com

# Check version info
python cli.py --version
```

### Performance Optimization
```bash
# Enable caching for better performance
python cli.py --config production-config.yaml

# Use multiple workers for parallel processing
python cli.py --model-workers 4

# Optimize for speed vs accuracy
python cli.py --confidence-threshold 0.2 --fast-mode
```

## 📊 Performance & Metrics

### Success Rates
- **Static Challenges**: 85-95% success rate
- **Dynamic Challenges**: 75-85% success rate
- **Complex Scenes**: 70-80% success rate

### Performance Metrics
- **Average Solve Time**: 8-15 seconds
- **Memory Usage**: 200-500MB
- **CPU Usage**: Moderate during detection phase
- **Network**: Minimal bandwidth requirements

### Monitoring
```python
# Built-in performance monitoring
result = solver.solve(url)
print(f"Success Rate: {result.get_success_rate():.1%}")
print(f"Average Time: {result.get_average_challenge_time():.2f}s")
print(f"Total Attempts: {result.total_attempts}")
```

## 🧪 Testing

### Running Tests
```bash
# Run all tests
pytest tests/

# Run with coverage
pytest --cov=recaptcha_solver tests/

# Run specific test categories
pytest tests/test_browser.py
pytest tests/test_detection.py
pytest tests/test_config.py
```

### Test Configuration
```bash
# Test with mock challenges
python cli.py --test-mode --mock-challenges

# Validate configuration
python cli.py --validate-config config.yaml

# Benchmark performance
python cli.py --benchmark --iterations 10
```

## 🚀 Deployment

### Docker Support
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "cli.py", "--config", "production.yaml"]
```

### Production Considerations
- Use headless mode for server deployment
- Configure appropriate timeouts and retries
- Monitor memory usage and implement cleanup
- Use configuration files instead of command line arguments
- Implement proper logging and monitoring

## ⚖️ Legal & Ethics

### Important Notice
This tool is designed for:
- **Educational purposes**: Learning about computer vision and automation
- **Research applications**: Academic and scientific research
- **Legitimate testing**: Testing your own applications
- **Accessibility**: Helping users with disabilities

### Responsible Usage
- Respect website terms of service
- Don't use for malicious purposes
- Consider rate limiting and ethical implications
- Ensure compliance with applicable laws

### Disclaimer
Users are solely responsible for ensuring their use complies with applicable laws, regulations, and terms of service. The developers assume no liability for misuse of this software.

## 🤝 Contributing

### Development Setup
```bash
# Clone and setup development environment
git clone <repository-url>
cd Recaptcha-Image
pip install -r requirements.txt

# Install development tools
pip install black flake8 mypy pytest

# Run code quality checks
black recaptcha_solver/
flake8 recaptcha_solver/
mypy recaptcha_solver/
```

### Contribution Guidelines
1. **Code Style**: Follow PEP 8 and use Black for formatting
2. **Testing**: Add tests for new features and bug fixes
3. **Documentation**: Update documentation for API changes
4. **Error Handling**: Use custom exceptions and proper logging
5. **Performance**: Consider performance implications of changes

### Areas for Contribution
- Additional browser support (Safari, Opera)
- New detection strategies and algorithms
- Performance optimizations
- Additional configuration options
- Better error recovery mechanisms
- Enhanced monitoring and analytics

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### Third-Party Licenses
- **Selenium**: Apache License 2.0
- **OpenCV**: Apache License 2.0
- **Ultralytics YOLO**: AGPL-3.0 License
- **Pillow**: PIL Software License

## 🙏 Acknowledgments

- **Ultralytics**: For the excellent YOLO implementation
- **Selenium Team**: For robust browser automation tools
- **OpenCV Community**: For comprehensive computer vision library
- **Contributors**: All developers who have contributed to this project

---

**⭐ If this project helps you, please consider giving it a star!**
