"""
Base browser class and interfaces.
"""

import logging
from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

from ..config.browser_config import BrowserConfig
from ..exceptions.browser import (
    BrowserError, BrowserTimeoutError, BrowserNavigationError,
    ElementNotFoundError, BrowserInitializationError
)


class BaseBrowser(ABC):
    """Base class for all browser implementations."""
    
    def __init__(self, config: BrowserConfig):
        self.config = config
        self.driver: Optional[WebDriver] = None
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self._is_initialized = False
    
    @abstractmethod
    def _create_driver(self) -> WebDriver:
        """Create and configure the browser driver. Must be implemented by subclasses."""
        pass
    
    @abstractmethod
    def get_browser_name(self) -> str:
        """Get the name of the browser. Must be implemented by subclasses."""
        pass
    
    def initialize(self) -> None:
        """Initialize the browser driver."""
        if self._is_initialized:
            return
        
        try:
            self.logger.info(f"Initializing {self.get_browser_name()} browser")
            self.driver = self._create_driver()
            self._configure_driver()
            self._is_initialized = True
            self.logger.info(f"{self.get_browser_name()} browser initialized successfully")
        
        except Exception as e:
            self.logger.error(f"Failed to initialize {self.get_browser_name()} browser: {e}")
            raise BrowserInitializationError(
                browser_name=self.get_browser_name(),
                reason=str(e),
                cause=e
            )
    
    def _configure_driver(self) -> None:
        """Configure the driver with common settings."""
        if not self.driver:
            return
        
        # Set timeouts
        self.driver.set_page_load_timeout(self.config.page_load_timeout)
        self.driver.implicitly_wait(self.config.implicit_wait)
        
        # Set window size
        if not self.config.headless:
            self.driver.set_window_size(self.config.window_width, self.config.window_height)
    
    def navigate_to(self, url: str) -> None:
        """Navigate to a URL."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        try:
            self.logger.info(f"Navigating to: {url}")
            self.driver.get(url)
            self.logger.debug(f"Successfully navigated to: {url}")
        
        except TimeoutException as e:
            raise BrowserTimeoutError(
                operation="page_load",
                timeout=self.config.page_load_timeout,
                cause=e
            )
        except WebDriverException as e:
            raise BrowserNavigationError(
                url=url,
                reason=str(e),
                cause=e
            )
    
    def find_element(self, by: By, value: str, timeout: Optional[float] = None) -> Any:
        """Find a single element with optional timeout."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        timeout = timeout or self.config.element_wait_timeout
        
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        
        except TimeoutException as e:
            raise ElementNotFoundError(
                selector=value,
                selector_type=by,
                timeout=timeout,
                cause=e
            )
    
    def find_elements(self, by: By, value: str, timeout: Optional[float] = None) -> List[Any]:
        """Find multiple elements with optional timeout."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        timeout = timeout or self.config.element_wait_timeout
        
        try:
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return self.driver.find_elements(by, value)
        
        except TimeoutException as e:
            raise ElementNotFoundError(
                selector=value,
                selector_type=by,
                timeout=timeout,
                cause=e
            )
    
    def wait_for_element_clickable(self, by: By, value: str, timeout: Optional[float] = None) -> Any:
        """Wait for element to be clickable."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        timeout = timeout or self.config.element_wait_timeout
        
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        
        except TimeoutException as e:
            raise ElementNotFoundError(
                selector=value,
                selector_type=by,
                timeout=timeout,
                cause=e
            )
    
    def switch_to_frame(self, frame_locator: tuple) -> None:
        """Switch to iframe."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        try:
            frame = self.find_element(*frame_locator)
            self.driver.switch_to.frame(frame)
        except Exception as e:
            raise BrowserError(f"Failed to switch to frame: {e}", cause=e)
    
    def switch_to_default_content(self) -> None:
        """Switch back to default content."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        try:
            self.driver.switch_to.default_content()
        except Exception as e:
            raise BrowserError(f"Failed to switch to default content: {e}", cause=e)
    
    def add_cookie(self, cookie: Dict[str, Any]) -> None:
        """Add a cookie to the browser."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        try:
            self.driver.add_cookie(cookie)
        except Exception as e:
            raise BrowserError(f"Failed to add cookie: {e}", cause=e)
    
    def get_cookies(self) -> List[Dict[str, Any]]:
        """Get all cookies from the browser."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        try:
            return self.driver.get_cookies()
        except Exception as e:
            raise BrowserError(f"Failed to get cookies: {e}", cause=e)
    
    def execute_script(self, script: str, *args) -> Any:
        """Execute JavaScript in the browser."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        try:
            return self.driver.execute_script(script, *args)
        except Exception as e:
            raise BrowserError(f"Failed to execute script: {e}", cause=e)
    
    def get_page_source(self) -> str:
        """Get the page source."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        try:
            return self.driver.page_source
        except Exception as e:
            raise BrowserError(f"Failed to get page source: {e}", cause=e)
    
    def get_current_url(self) -> str:
        """Get the current URL."""
        if not self.driver:
            raise BrowserError("Browser not initialized")
        
        try:
            return self.driver.current_url
        except Exception as e:
            raise BrowserError(f"Failed to get current URL: {e}", cause=e)
    
    def close(self) -> None:
        """Close the browser."""
        if self.driver:
            try:
                self.logger.info(f"Closing {self.get_browser_name()} browser")
                self.driver.quit()
                self.logger.debug(f"{self.get_browser_name()} browser closed successfully")
            except Exception as e:
                self.logger.warning(f"Error closing browser: {e}")
            finally:
                self.driver = None
                self._is_initialized = False
    
    def __enter__(self):
        """Context manager entry."""
        self.initialize()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
    
    @property
    def is_initialized(self) -> bool:
        """Check if browser is initialized."""
        return self._is_initialized and self.driver is not None
