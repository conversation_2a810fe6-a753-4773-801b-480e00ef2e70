"""
Result classes for solver operations.
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, List, Any
from datetime import datetime
from enum import Enum


class SolverStatus(Enum):
    """Status of solver operation."""
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    ERROR = "error"
    CANCELLED = "cancelled"


@dataclass
class DetectionResult:
    """Result of object detection operation."""
    target_class: int
    confidence: float
    bounding_box: tuple
    grid_cell: int
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "target_class": self.target_class,
            "confidence": self.confidence,
            "bounding_box": self.bounding_box,
            "grid_cell": self.grid_cell
        }


@dataclass
class ChallengeResult:
    """Result of a single challenge attempt."""
    challenge_type: str
    target_description: str
    target_class: int
    detections: List[DetectionResult] = field(default_factory=list)
    selected_cells: List[int] = field(default_factory=list)
    success: bool = False
    error: Optional[str] = None
    processing_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "challenge_type": self.challenge_type,
            "target_description": self.target_description,
            "target_class": self.target_class,
            "detections": [d.to_dict() for d in self.detections],
            "selected_cells": self.selected_cells,
            "success": self.success,
            "error": self.error,
            "processing_time": self.processing_time
        }


@dataclass
class SolverResult:
    """Main result class for solver operations."""
    
    # Basic result info
    status: SolverStatus
    success: bool = False
    
    # Timing information
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    total_time: float = 0.0
    
    # reCAPTCHA data
    recaptcha_token: Optional[str] = None
    site_key: Optional[str] = None
    
    # Challenge information
    challenges: List[ChallengeResult] = field(default_factory=list)
    total_attempts: int = 0
    successful_attempts: int = 0
    
    # Browser data
    cookies: List[Dict[str, Any]] = field(default_factory=list)
    user_agent: Optional[str] = None
    
    # Error information
    error: Optional[str] = None
    error_code: Optional[str] = None
    error_details: Dict[str, Any] = field(default_factory=dict)
    
    # Performance metrics
    metrics: Dict[str, Any] = field(default_factory=dict)
    
    def mark_completed(self, success: bool = True) -> None:
        """Mark the result as completed."""
        self.end_time = datetime.now()
        self.total_time = (self.end_time - self.start_time).total_seconds()
        self.success = success
        self.status = SolverStatus.SUCCESS if success else SolverStatus.FAILED
    
    def add_challenge_result(self, challenge: ChallengeResult) -> None:
        """Add a challenge result."""
        self.challenges.append(challenge)
        self.total_attempts += 1
        if challenge.success:
            self.successful_attempts += 1
    
    def set_error(self, error: str, error_code: Optional[str] = None, 
                  details: Optional[Dict[str, Any]] = None) -> None:
        """Set error information."""
        self.error = error
        self.error_code = error_code
        if details:
            self.error_details.update(details)
        self.status = SolverStatus.ERROR
        self.success = False
    
    def add_metric(self, key: str, value: Any) -> None:
        """Add a performance metric."""
        self.metrics[key] = value
    
    def get_success_rate(self) -> float:
        """Get the success rate of challenges."""
        if self.total_attempts == 0:
            return 0.0
        return self.successful_attempts / self.total_attempts
    
    def get_average_challenge_time(self) -> float:
        """Get average time per challenge."""
        if not self.challenges:
            return 0.0
        
        total_time = sum(c.processing_time for c in self.challenges)
        return total_time / len(self.challenges)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            "status": self.status.value,
            "success": self.success,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "total_time": self.total_time,
            "recaptcha_token": self.recaptcha_token,
            "site_key": self.site_key,
            "challenges": [c.to_dict() for c in self.challenges],
            "total_attempts": self.total_attempts,
            "successful_attempts": self.successful_attempts,
            "success_rate": self.get_success_rate(),
            "average_challenge_time": self.get_average_challenge_time(),
            "cookies": self.cookies,
            "user_agent": self.user_agent,
            "error": self.error,
            "error_code": self.error_code,
            "error_details": self.error_details,
            "metrics": self.metrics
        }
    
    def __str__(self) -> str:
        """String representation of the result."""
        if self.success:
            return f"SolverResult(SUCCESS, {self.total_attempts} attempts, {self.total_time:.2f}s)"
        else:
            return f"SolverResult(FAILED, {self.error or 'Unknown error'})"
