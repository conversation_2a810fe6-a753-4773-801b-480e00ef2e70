"""
Model configuration settings.
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, List
from pathlib import Path
from .base import BaseConfig, ConfigValidator
from ..exceptions.config import InvalidConfigError


@dataclass
class ModelConfig(BaseConfig):
    """Configuration for model settings."""
    
    # Model paths
    primary_model_path: Path = Path("./models/models.onnx")
    fallback_model_paths: List[Path] = field(default_factory=list)
    
    # Model loading settings
    lazy_loading: bool = True
    cache_models: bool = True
    max_cache_size: int = 3
    
    # Prediction settings
    confidence_threshold: float = 0.25
    iou_threshold: float = 0.1
    max_detections: int = 100
    
    # Performance settings
    use_gpu: bool = True
    batch_size: int = 1
    num_threads: Optional[int] = None
    
    # Model-specific settings
    model_format: str = "onnx"
    input_size: tuple = (640, 640)
    
    # Preprocessing settings
    normalize: bool = True
    mean: tuple = (0.485, 0.456, 0.406)
    std: tuple = (0.229, 0.224, 0.225)
    
    # Postprocessing settings
    nms_threshold: float = 0.45
    class_agnostic_nms: bool = False
    
    # Retry settings
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # Timeout settings
    load_timeout: float = 30.0
    prediction_timeout: float = 10.0
    
    def validate(self) -> None:
        """Validate model configuration."""
        # Validate primary model path
        if self.primary_model_path:
            self.primary_model_path = ConfigValidator.validate_path(
                self.primary_model_path, must_exist=True, field_name="primary_model_path"
            )
        
        # Validate fallback model paths
        validated_fallbacks = []
        for i, path in enumerate(self.fallback_model_paths):
            validated_path = ConfigValidator.validate_path(
                path, must_exist=True, field_name=f"fallback_model_paths[{i}]"
            )
            validated_fallbacks.append(validated_path)
        self.fallback_model_paths = validated_fallbacks
        
        # Validate thresholds
        ConfigValidator.validate_range(
            self.confidence_threshold, 0.0, 1.0, "confidence_threshold"
        )
        ConfigValidator.validate_range(
            self.iou_threshold, 0.0, 1.0, "iou_threshold"
        )
        ConfigValidator.validate_range(
            self.nms_threshold, 0.0, 1.0, "nms_threshold"
        )
        
        # Validate counts
        ConfigValidator.validate_range(
            self.max_detections, 1, 1000, "max_detections"
        )
        ConfigValidator.validate_range(
            self.max_cache_size, 1, 10, "max_cache_size"
        )
        ConfigValidator.validate_range(
            self.batch_size, 1, 32, "batch_size"
        )
        
        # Validate retries
        ConfigValidator.validate_range(
            self.max_retries, 0, 10, "max_retries"
        )
        ConfigValidator.validate_range(
            self.retry_delay, 0.0, 10.0, "retry_delay"
        )
        
        # Validate timeouts
        ConfigValidator.validate_range(
            self.load_timeout, 1.0, 300.0, "load_timeout"
        )
        ConfigValidator.validate_range(
            self.prediction_timeout, 1.0, 60.0, "prediction_timeout"
        )
        
        # Validate model format
        valid_formats = ["onnx", "pytorch", "tensorflow"]
        ConfigValidator.validate_choices(
            self.model_format, valid_formats, "model_format"
        )
        
        # Validate input size
        if not isinstance(self.input_size, tuple) or len(self.input_size) != 2:
            raise InvalidConfigError(
                config_key="input_size",
                config_value=self.input_size,
                expected_type="tuple of 2 integers"
            )
        
        # Validate normalization parameters
        if not isinstance(self.mean, tuple) or len(self.mean) != 3:
            raise InvalidConfigError(
                config_key="mean",
                config_value=self.mean,
                expected_type="tuple of 3 floats"
            )
        
        if not isinstance(self.std, tuple) or len(self.std) != 3:
            raise InvalidConfigError(
                config_key="std",
                config_value=self.std,
                expected_type="tuple of 3 floats"
            )
        
        # Validate thread count
        if self.num_threads is not None:
            ConfigValidator.validate_range(
                self.num_threads, 1, 32, "num_threads"
            )
    
    def get_all_model_paths(self) -> List[Path]:
        """Get all model paths (primary + fallbacks)."""
        paths = [self.primary_model_path]
        paths.extend(self.fallback_model_paths)
        return paths
    
    def get_available_model_paths(self) -> List[Path]:
        """Get only the model paths that exist."""
        return [path for path in self.get_all_model_paths() if path.exists()]
