"""
Timing utilities and timeout management.
"""

import time
import signal
import logging
from typing import Optional, Callable, Any
from contextlib import contextmanager
from threading import Timer as ThreadTimer


class Timer:
    """High-precision timer for performance measurement."""
    
    def __init__(self):
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.elapsed_time: float = 0.0
    
    def start(self) -> None:
        """Start the timer."""
        self.start_time = time.perf_counter()
        self.end_time = None
        self.elapsed_time = 0.0
    
    def stop(self) -> float:
        """Stop the timer and return elapsed time."""
        if self.start_time is None:
            raise ValueError("Timer not started")
        
        self.end_time = time.perf_counter()
        self.elapsed_time = self.end_time - self.start_time
        return self.elapsed_time
    
    def elapsed(self) -> float:
        """Get current elapsed time without stopping the timer."""
        if self.start_time is None:
            return 0.0
        
        current_time = time.perf_counter()
        return current_time - self.start_time
    
    def reset(self) -> None:
        """Reset the timer."""
        self.start_time = None
        self.end_time = None
        self.elapsed_time = 0.0
    
    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()


class TimeoutManager:
    """Manages timeouts for operations."""
    
    def __init__(self, timeout: float):
        self.timeout = timeout
        self.logger = logging.getLogger(__name__)
        self._timer: Optional[ThreadTimer] = None
        self._timed_out = False
    
    @contextmanager
    def timeout_context(self):
        """Context manager for timeout operations."""
        self._timed_out = False
        
        def timeout_handler():
            self._timed_out = True
            self.logger.warning(f"Operation timed out after {self.timeout} seconds")
        
        self._timer = ThreadTimer(self.timeout, timeout_handler)
        self._timer.start()
        
        try:
            yield self
        finally:
            if self._timer:
                self._timer.cancel()
    
    def is_timed_out(self) -> bool:
        """Check if the operation has timed out."""
        return self._timed_out
    
    def check_timeout(self) -> None:
        """Raise TimeoutError if timed out."""
        if self._timed_out:
            raise TimeoutError(f"Operation timed out after {self.timeout} seconds")


def timeout(seconds: float):
    """Decorator for adding timeout to functions."""
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            timeout_manager = TimeoutManager(seconds)
            
            with timeout_manager.timeout_context():
                result = func(*args, **kwargs)
                timeout_manager.check_timeout()
                return result
        
        return wrapper
    return decorator


class PerformanceMonitor:
    """Monitor performance metrics for operations."""
    
    def __init__(self):
        self.metrics = {}
        self.logger = logging.getLogger(__name__)
    
    def time_operation(self, operation_name: str):
        """Context manager for timing operations."""
        return TimedOperation(operation_name, self)
    
    def record_metric(self, name: str, value: float, unit: str = "seconds") -> None:
        """Record a performance metric."""
        if name not in self.metrics:
            self.metrics[name] = []
        
        self.metrics[name].append({
            "value": value,
            "unit": unit,
            "timestamp": time.time()
        })
        
        self.logger.debug(f"Recorded metric {name}: {value} {unit}")
    
    def get_average(self, metric_name: str) -> Optional[float]:
        """Get average value for a metric."""
        if metric_name not in self.metrics:
            return None
        
        values = [m["value"] for m in self.metrics[metric_name]]
        return sum(values) / len(values) if values else None
    
    def get_total(self, metric_name: str) -> Optional[float]:
        """Get total value for a metric."""
        if metric_name not in self.metrics:
            return None
        
        values = [m["value"] for m in self.metrics[metric_name]]
        return sum(values)
    
    def get_count(self, metric_name: str) -> int:
        """Get count of recorded values for a metric."""
        return len(self.metrics.get(metric_name, []))
    
    def clear_metrics(self) -> None:
        """Clear all recorded metrics."""
        self.metrics.clear()
    
    def get_summary(self) -> dict:
        """Get summary of all metrics."""
        summary = {}
        
        for name, values in self.metrics.items():
            if values:
                metric_values = [v["value"] for v in values]
                summary[name] = {
                    "count": len(metric_values),
                    "total": sum(metric_values),
                    "average": sum(metric_values) / len(metric_values),
                    "min": min(metric_values),
                    "max": max(metric_values),
                    "unit": values[0]["unit"] if values else "unknown"
                }
        
        return summary


class TimedOperation:
    """Context manager for timing operations with automatic metric recording."""
    
    def __init__(self, operation_name: str, monitor: PerformanceMonitor):
        self.operation_name = operation_name
        self.monitor = monitor
        self.timer = Timer()
    
    def __enter__(self):
        self.timer.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        elapsed = self.timer.stop()
        self.monitor.record_metric(self.operation_name, elapsed)
        
        if exc_type is None:
            self.monitor.logger.debug(
                f"Operation '{self.operation_name}' completed in {elapsed:.3f}s"
            )
        else:
            self.monitor.logger.warning(
                f"Operation '{self.operation_name}' failed after {elapsed:.3f}s: {exc_val}"
            )
