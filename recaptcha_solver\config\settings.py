"""
Main configuration settings for the reCAPTCHA solver.
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
from pathlib import Path
from .base import BaseConfig, ConfigValidator
from .browser_config import BrowserConfig
from .model_config import ModelConfig
from .detection_config import DetectionConfig


@dataclass
class SolverConfig(BaseConfig):
    """Main configuration class for the reCAPTCHA solver."""
    
    # Sub-configurations
    browser: BrowserConfig = field(default_factory=BrowserConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    detection: DetectionConfig = field(default_factory=DetectionConfig)
    
    # General settings
    max_solve_attempts: int = 10
    attempt_delay: float = 2.0
    
    # Logging settings
    log_level: str = "INFO"
    log_file: Optional[Path] = None
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    enable_file_logging: bool = True
    enable_console_logging: bool = True
    
    # Performance settings
    cleanup_temp_files: bool = True
    temp_dir: Optional[Path] = None
    
    # Monitoring settings
    enable_monitoring: bool = True
    metrics_file: Optional[Path] = None
    
    # Security settings
    verify_ssl: bool = False
    allow_redirects: bool = True
    
    # API settings (for future API interface)
    api_enabled: bool = False
    api_host: str = "localhost"
    api_port: int = 8080
    api_workers: int = 1
    
    # Rate limiting
    rate_limit_enabled: bool = False
    requests_per_minute: int = 60
    
    def validate(self) -> None:
        """Validate the main configuration."""
        # Validate sub-configurations
        self.browser.validate()
        self.model.validate()
        self.detection.validate()
        
        # Validate general settings
        ConfigValidator.validate_range(
            self.max_solve_attempts, 1, 50, "max_solve_attempts"
        )
        ConfigValidator.validate_range(
            self.attempt_delay, 0.0, 30.0, "attempt_delay"
        )
        
        # Validate log level
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        ConfigValidator.validate_choices(
            self.log_level, valid_log_levels, "log_level"
        )
        
        # Validate paths
        if self.log_file:
            # Ensure parent directory exists
            self.log_file = Path(self.log_file)
            self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
        if self.temp_dir:
            self.temp_dir = Path(self.temp_dir)
            self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        if self.metrics_file:
            self.metrics_file = Path(self.metrics_file)
            self.metrics_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Validate API settings
        ConfigValidator.validate_range(
            self.api_port, 1024, 65535, "api_port"
        )
        ConfigValidator.validate_range(
            self.api_workers, 1, 16, "api_workers"
        )
        
        # Validate rate limiting
        ConfigValidator.validate_range(
            self.requests_per_minute, 1, 1000, "requests_per_minute"
        )
    
    @classmethod
    def create_default(cls) -> 'SolverConfig':
        """Create a default configuration."""
        return cls()
    
    @classmethod
    def create_for_development(cls) -> 'SolverConfig':
        """Create a configuration optimized for development."""
        config = cls()
        config.log_level = "DEBUG"
        config.browser.headless = False
        config.enable_monitoring = True
        config.cleanup_temp_files = False
        return config
    
    @classmethod
    def create_for_production(cls) -> 'SolverConfig':
        """Create a configuration optimized for production."""
        config = cls()
        config.log_level = "WARNING"
        config.browser.headless = True
        config.enable_monitoring = True
        config.cleanup_temp_files = True
        config.rate_limit_enabled = True
        return config
    
    @classmethod
    def create_for_testing(cls) -> 'SolverConfig':
        """Create a configuration optimized for testing."""
        config = cls()
        config.log_level = "ERROR"
        config.browser.headless = True
        config.enable_monitoring = False
        config.cleanup_temp_files = True
        config.max_solve_attempts = 3
        return config
    
    def get_effective_temp_dir(self) -> Path:
        """Get the effective temporary directory."""
        if self.temp_dir:
            return self.temp_dir
        
        import tempfile
        return Path(tempfile.gettempdir()) / "recaptcha_solver"
    
    def setup_logging(self) -> None:
        """Setup logging based on configuration."""
        import logging
        import sys
        
        # Create logger
        logger = logging.getLogger("recaptcha_solver")
        logger.setLevel(getattr(logging, self.log_level))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(self.log_format)
        
        # Add console handler
        if self.enable_console_logging:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # Add file handler
        if self.enable_file_logging and self.log_file:
            file_handler = logging.FileHandler(self.log_file)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary with nested configs."""
        result = super().to_dict()
        
        # Convert nested configs
        if hasattr(self, 'browser') and self.browser:
            result['browser'] = self.browser.to_dict()
        if hasattr(self, 'model') and self.model:
            result['model'] = self.model.to_dict()
        if hasattr(self, 'detection') and self.detection:
            result['detection'] = self.detection.to_dict()
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SolverConfig':
        """Create configuration from dictionary with nested configs."""
        # Extract nested configs
        browser_data = data.pop('browser', {})
        model_data = data.pop('model', {})
        detection_data = data.pop('detection', {})
        
        # Create main config
        config = super().from_dict(data)
        
        # Create nested configs
        if browser_data:
            config.browser = BrowserConfig.from_dict(browser_data)
        if model_data:
            config.model = ModelConfig.from_dict(model_data)
        if detection_data:
            config.detection = DetectionConfig.from_dict(detection_data)
        
        return config
