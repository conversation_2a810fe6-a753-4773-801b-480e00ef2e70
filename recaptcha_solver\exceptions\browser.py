"""
Browser-related exception classes.
"""

from typing import Optional, Dict, Any
from .base import RecaptchaSolverError, RetryableError, NonRetryableError


class BrowserError(RecaptchaSolverError):
    """Base class for browser-related errors."""
    pass


class BrowserNotFoundError(BrowserError, NonRetryableError):
    """Raised when the specified browser is not found or not supported."""
    
    def __init__(
        self, 
        browser_name: str, 
        available_browsers: Optional[list] = None,
        **kwargs
    ):
        message = f"Browser '{browser_name}' not found or not supported"
        if available_browsers:
            message += f". Available browsers: {', '.join(available_browsers)}"
        
        super().__init__(
            message,
            details={
                "browser_name": browser_name,
                "available_browsers": available_browsers or []
            },
            **kwargs
        )


class BrowserTimeoutError(BrowserError, RetryableError):
    """Raised when browser operations timeout."""
    
    def __init__(
        self, 
        operation: str, 
        timeout: float,
        **kwargs
    ):
        message = f"Browser operation '{operation}' timed out after {timeout} seconds"
        super().__init__(
            message,
            details={
                "operation": operation,
                "timeout": timeout
            },
            **kwargs
        )


class BrowserInitializationError(BrowserError, RetryableError):
    """Raised when browser fails to initialize."""
    
    def __init__(
        self, 
        browser_name: str, 
        reason: Optional[str] = None,
        **kwargs
    ):
        message = f"Failed to initialize {browser_name} browser"
        if reason:
            message += f": {reason}"
        
        super().__init__(
            message,
            details={
                "browser_name": browser_name,
                "reason": reason
            },
            **kwargs
        )


class BrowserNavigationError(BrowserError, RetryableError):
    """Raised when browser navigation fails."""
    
    def __init__(
        self, 
        url: str, 
        reason: Optional[str] = None,
        **kwargs
    ):
        message = f"Failed to navigate to URL: {url}"
        if reason:
            message += f" ({reason})"
        
        super().__init__(
            message,
            details={
                "url": url,
                "reason": reason
            },
            **kwargs
        )


class ElementNotFoundError(BrowserError, RetryableError):
    """Raised when a required element is not found on the page."""
    
    def __init__(
        self, 
        selector: str, 
        selector_type: str = "xpath",
        timeout: Optional[float] = None,
        **kwargs
    ):
        message = f"Element not found: {selector} (type: {selector_type})"
        if timeout:
            message += f" after {timeout} seconds"
        
        super().__init__(
            message,
            details={
                "selector": selector,
                "selector_type": selector_type,
                "timeout": timeout
            },
            **kwargs
        )
