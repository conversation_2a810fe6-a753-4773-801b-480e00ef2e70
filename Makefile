# Makefile for reCAPTCHA Solver

.PHONY: help install install-dev test lint format clean build upload docs

# Default target
help:
	@echo "Available targets:"
	@echo "  install      - Install package and dependencies"
	@echo "  install-dev  - Install package with development dependencies"
	@echo "  test         - Run tests"
	@echo "  lint         - Run linting (flake8, mypy)"
	@echo "  format       - Format code with black"
	@echo "  clean        - Clean build artifacts and cache"
	@echo "  build        - Build package"
	@echo "  upload       - Upload package to PyPI"
	@echo "  docs         - Generate documentation"
	@echo "  demo         - Run demo example"
	@echo "  check-config - Test configuration"

# Installation targets
install:
	pip install -e .

install-dev:
	pip install -e .[dev]

# Testing targets
test:
	pytest tests/ -v --cov=recaptcha_solver --cov-report=html --cov-report=term

test-quick:
	pytest tests/ -v -x

# Code quality targets
lint:
	flake8 recaptcha_solver/ cli.py example.py
	mypy recaptcha_solver/

format:
	black recaptcha_solver/ cli.py example.py setup.py

format-check:
	black --check recaptcha_solver/ cli.py example.py setup.py

# Cleaning targets
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf __pycache__/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .mypy_cache/
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -name "*.log" -delete
	find . -name "*.png" -delete
	find . -name "*.jpg" -delete

# Build targets
build: clean
	python setup.py sdist bdist_wheel

upload: build
	twine upload dist/*

upload-test: build
	twine upload --repository testpypi dist/*

# Documentation targets
docs:
	@echo "Documentation is in README.md and REFACTORING_SUMMARY.md"

# Demo targets
demo:
	python example.py

check-config:
	python cli.py --test-config

# Development targets
dev-setup: install-dev
	@echo "Development environment setup complete"

pre-commit: format lint test
	@echo "Pre-commit checks passed"

# Release targets
release-patch:
	bump2version patch

release-minor:
	bump2version minor

release-major:
	bump2version major

# Docker targets (if needed in future)
docker-build:
	docker build -t recaptcha-solver .

docker-run:
	docker run -it --rm recaptcha-solver

# Utility targets
requirements:
	pip freeze > requirements-freeze.txt

check-deps:
	pip check

update-deps:
	pip install --upgrade -r requirements.txt
