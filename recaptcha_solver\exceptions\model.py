"""
Model-related exception classes.
"""

from typing import Optional
from .base import RecaptchaSolverError, RetryableError, NonRetryableError


class ModelError(RecaptchaSolverError):
    """Base class for model-related errors."""
    pass


class ModelNotFoundError(ModelError, NonRetryableError):
    """Raised when the model file is not found."""
    
    def __init__(
        self, 
        model_path: str,
        **kwargs
    ):
        message = f"Model file not found: {model_path}"
        super().__init__(
            message,
            details={"model_path": model_path},
            **kwargs
        )


class ModelLoadError(ModelError, RetryableError):
    """Raised when model fails to load."""
    
    def __init__(
        self, 
        model_path: str, 
        reason: Optional[str] = None,
        **kwargs
    ):
        message = f"Failed to load model from: {model_path}"
        if reason:
            message += f" ({reason})"
        
        super().__init__(
            message,
            details={
                "model_path": model_path,
                "reason": reason
            },
            **kwargs
        )


class ModelPredictionError(ModelError, RetryableError):
    """Raised when model prediction fails."""
    
    def __init__(
        self, 
        model_name: str, 
        reason: Optional[str] = None,
        **kwargs
    ):
        message = f"Model prediction failed: {model_name}"
        if reason:
            message += f" ({reason})"
        
        super().__init__(
            message,
            details={
                "model_name": model_name,
                "reason": reason
            },
            **kwargs
        )


class UnsupportedModelFormatError(ModelError, NonRetryableError):
    """Raised when model format is not supported."""
    
    def __init__(
        self, 
        model_path: str, 
        supported_formats: Optional[list] = None,
        **kwargs
    ):
        message = f"Unsupported model format: {model_path}"
        if supported_formats:
            message += f". Supported formats: {', '.join(supported_formats)}"
        
        super().__init__(
            message,
            details={
                "model_path": model_path,
                "supported_formats": supported_formats or []
            },
            **kwargs
        )
