"""
Exception classes for the reCAPTCHA solver.
"""

from .base import RecaptchaSolverError
from .browser import BrowserError, BrowserNotFoundError, BrowserTimeoutError
from .model import ModelError, ModelNotFoundError, ModelLoadError
from .image import ImageError, ImageDownloadError, ImageProcessingError
from .detection import DetectionError, NoDetectionError, InvalidDetectionError
from .config import ConfigError, InvalidConfigError

__all__ = [
    "RecaptchaSolverError",
    "BrowserError",
    "BrowserNotFoundError", 
    "BrowserTimeoutError",
    "ModelError",
    "ModelNotFoundError",
    "ModelLoadError",
    "ImageError",
    "ImageDownloadError",
    "ImageProcessingError",
    "DetectionError",
    "NoDetectionError",
    "InvalidDetectionError",
    "ConfigError",
    "InvalidConfigError",
]
