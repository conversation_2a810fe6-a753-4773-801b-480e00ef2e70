"""
Image enhancement and preprocessing utilities.
"""

import logging
from pathlib import Path
from typing import Op<PERSON>, <PERSON><PERSON>, Union
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter

from ..exceptions.image import ImageProcessingError


class ImageEnhancer:
    """Handles image enhancement and preprocessing operations."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def enhance_image(self, image_path: Path, output_path: Optional[Path] = None, 
                     enhance_contrast: bool = True, reduce_noise: bool = True,
                     sharpen: bool = True) -> Path:
        """
        Apply various enhancement techniques to an image.
        
        Args:
            image_path: Path to input image
            output_path: Path for output image (if None, overwrites input)
            enhance_contrast: Whether to enhance contrast
            reduce_noise: Whether to reduce noise
            sharpen: Whether to apply sharpening
            
        Returns:
            Path to the enhanced image
            
        Raises:
            ImageProcessingError: If enhancement fails
        """
        if output_path is None:
            output_path = image_path
        
        try:
            self.logger.debug(f"Enhancing image: {image_path}")
            
            # Load image with OpenCV
            image = cv2.imread(str(image_path))
            if image is None:
                raise ImageProcessingError(
                    operation="load_image",
                    image_path=str(image_path),
                    reason="Failed to load image with OpenCV"
                )
            
            # Apply enhancements
            if enhance_contrast:
                image = self._enhance_contrast_clahe(image)
            
            if reduce_noise:
                image = self._reduce_noise(image)
            
            if sharpen:
                image = self._apply_sharpening(image)
            
            # Save enhanced image
            success = cv2.imwrite(str(output_path), image)
            if not success:
                raise ImageProcessingError(
                    operation="save_image",
                    image_path=str(output_path),
                    reason="Failed to save enhanced image"
                )
            
            self.logger.debug(f"Image enhanced successfully: {output_path}")
            return output_path
        
        except Exception as e:
            if isinstance(e, ImageProcessingError):
                raise
            raise ImageProcessingError(
                operation="enhance_image",
                image_path=str(image_path),
                reason=str(e),
                cause=e
            )
    
    def _enhance_contrast_clahe(self, image: np.ndarray) -> np.ndarray:
        """Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)."""
        try:
            # Convert to LAB color space
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            l_channel, a, b = cv2.split(lab)
            
            # Apply CLAHE to L-channel
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
            l_channel = clahe.apply(l_channel)
            
            # Merge channels and convert back to BGR
            enhanced_image = cv2.merge((l_channel, a, b))
            enhanced_image = cv2.cvtColor(enhanced_image, cv2.COLOR_LAB2BGR)
            
            return enhanced_image
        
        except Exception as e:
            self.logger.warning(f"CLAHE enhancement failed: {e}")
            return image
    
    def _reduce_noise(self, image: np.ndarray) -> np.ndarray:
        """Apply noise reduction using bilateral filter."""
        try:
            # Apply bilateral filter for noise reduction while preserving edges
            denoised = cv2.bilateralFilter(image, 9, 75, 75)
            return denoised
        
        except Exception as e:
            self.logger.warning(f"Noise reduction failed: {e}")
            return image
    
    def _apply_sharpening(self, image: np.ndarray) -> np.ndarray:
        """Apply sharpening filter."""
        try:
            # Create sharpening kernel
            kernel = np.array([[-1, -1, -1],
                              [-1,  9, -1],
                              [-1, -1, -1]])
            
            # Apply sharpening
            sharpened = cv2.filter2D(image, -1, kernel)
            return sharpened
        
        except Exception as e:
            self.logger.warning(f"Sharpening failed: {e}")
            return image
    
    def resize_image(self, image_path: Path, target_size: Tuple[int, int], 
                    output_path: Optional[Path] = None, maintain_aspect: bool = True) -> Path:
        """
        Resize an image to target dimensions.
        
        Args:
            image_path: Path to input image
            target_size: Target (width, height)
            output_path: Path for output image
            maintain_aspect: Whether to maintain aspect ratio
            
        Returns:
            Path to resized image
        """
        if output_path is None:
            output_path = image_path
        
        try:
            with Image.open(image_path) as img:
                if maintain_aspect:
                    img.thumbnail(target_size, Image.Resampling.LANCZOS)
                else:
                    img = img.resize(target_size, Image.Resampling.LANCZOS)
                
                img.save(output_path)
            
            return output_path
        
        except Exception as e:
            raise ImageProcessingError(
                operation="resize_image",
                image_path=str(image_path),
                reason=str(e),
                cause=e
            )
    
    def normalize_image(self, image_path: Path, output_path: Optional[Path] = None) -> Path:
        """
        Normalize image pixel values to 0-255 range.
        
        Args:
            image_path: Path to input image
            output_path: Path for output image
            
        Returns:
            Path to normalized image
        """
        if output_path is None:
            output_path = image_path
        
        try:
            image = cv2.imread(str(image_path))
            if image is None:
                raise ImageProcessingError(
                    operation="load_image",
                    image_path=str(image_path)
                )
            
            # Normalize to 0-255 range
            normalized = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX)
            
            cv2.imwrite(str(output_path), normalized)
            return output_path
        
        except Exception as e:
            raise ImageProcessingError(
                operation="normalize_image",
                image_path=str(image_path),
                reason=str(e),
                cause=e
            )
    
    def convert_to_rgb(self, image_path: Path, output_path: Optional[Path] = None) -> Path:
        """
        Convert image to RGB format.
        
        Args:
            image_path: Path to input image
            output_path: Path for output image
            
        Returns:
            Path to converted image
        """
        if output_path is None:
            output_path = image_path
        
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if not already
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                img.save(output_path)
            
            return output_path
        
        except Exception as e:
            raise ImageProcessingError(
                operation="convert_to_rgb",
                image_path=str(image_path),
                reason=str(e),
                cause=e
            )
    
    def crop_image(self, image_path: Path, bbox: Tuple[int, int, int, int], 
                  output_path: Optional[Path] = None) -> Path:
        """
        Crop image to specified bounding box.
        
        Args:
            image_path: Path to input image
            bbox: Bounding box as (x1, y1, x2, y2)
            output_path: Path for output image
            
        Returns:
            Path to cropped image
        """
        if output_path is None:
            output_path = image_path
        
        try:
            with Image.open(image_path) as img:
                cropped = img.crop(bbox)
                cropped.save(output_path)
            
            return output_path
        
        except Exception as e:
            raise ImageProcessingError(
                operation="crop_image",
                image_path=str(image_path),
                reason=str(e),
                cause=e
            )
    
    def adjust_brightness_contrast(self, image_path: Path, brightness: float = 1.0, 
                                  contrast: float = 1.0, output_path: Optional[Path] = None) -> Path:
        """
        Adjust image brightness and contrast.
        
        Args:
            image_path: Path to input image
            brightness: Brightness factor (1.0 = no change)
            contrast: Contrast factor (1.0 = no change)
            output_path: Path for output image
            
        Returns:
            Path to adjusted image
        """
        if output_path is None:
            output_path = image_path
        
        try:
            with Image.open(image_path) as img:
                # Adjust brightness
                if brightness != 1.0:
                    enhancer = ImageEnhance.Brightness(img)
                    img = enhancer.enhance(brightness)
                
                # Adjust contrast
                if contrast != 1.0:
                    enhancer = ImageEnhance.Contrast(img)
                    img = enhancer.enhance(contrast)
                
                img.save(output_path)
            
            return output_path
        
        except Exception as e:
            raise ImageProcessingError(
                operation="adjust_brightness_contrast",
                image_path=str(image_path),
                reason=str(e),
                cause=e
            )
