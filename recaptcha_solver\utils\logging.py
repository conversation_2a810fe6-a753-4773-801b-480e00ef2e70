"""
Advanced logging utilities and formatters.
"""

import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime


class ColoredFormatter(logging.Formatter):
    """Colored console formatter for better readability."""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}{record.levelname}"
                f"{self.COLORS['RESET']}"
            )
        
        return super().format(record)


class StructuredFormatter(logging.Formatter):
    """JSON-like structured formatter for machine-readable logs."""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                          'pathname', 'filename', 'module', 'lineno', 'funcName',
                          'created', 'msecs', 'relativeCreated', 'thread',
                          'threadName', 'processName', 'process', 'getMessage',
                          'exc_info', 'exc_text', 'stack_info']:
                log_entry[key] = value
        
        import json
        return json.dumps(log_entry)


class PerformanceFilter(logging.Filter):
    """Filter to add performance metrics to log records."""
    
    def __init__(self):
        super().__init__()
        self.start_time = datetime.now()
    
    def filter(self, record):
        # Add elapsed time since logger creation
        elapsed = (datetime.now() - self.start_time).total_seconds()
        record.elapsed = f"{elapsed:.3f}s"
        return True


def setup_logger(
    name: str,
    level: str = "INFO",
    log_file: Optional[Path] = None,
    console_output: bool = True,
    structured_format: bool = False,
    colored_output: bool = True
) -> logging.Logger:
    """
    Setup a logger with advanced configuration.
    
    Args:
        name: Logger name
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional file path for file logging
        console_output: Whether to output to console
        structured_format: Whether to use structured JSON format
        colored_output: Whether to use colored console output
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    if structured_format:
        formatter = StructuredFormatter()
    else:
        format_string = (
            "%(asctime)s - %(name)s - %(levelname)s - "
            "%(module)s:%(funcName)s:%(lineno)d - %(message)s"
        )
        
        if colored_output and console_output:
            formatter = ColoredFormatter(format_string)
        else:
            formatter = logging.Formatter(format_string)
    
    # Add performance filter
    perf_filter = PerformanceFilter()
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.addFilter(perf_filter)
        logger.addHandler(console_handler)
    
    # File handler
    if log_file:
        log_file = Path(log_file)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        
        # Always use structured format for file logging
        if not structured_format:
            file_formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - "
                "%(module)s:%(funcName)s:%(lineno)d - %(message)s"
            )
        else:
            file_formatter = formatter
        
        file_handler.setFormatter(file_formatter)
        file_handler.addFilter(perf_filter)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """Get an existing logger or create a basic one."""
    logger = logging.getLogger(name)
    
    # If logger has no handlers, set up basic configuration
    if not logger.handlers:
        logger = setup_logger(name)
    
    return logger


class LogContext:
    """Context manager for adding context to log messages."""
    
    def __init__(self, logger: logging.Logger, **context):
        self.logger = logger
        self.context = context
        self.old_factory = None
    
    def __enter__(self):
        self.old_factory = logging.getLogRecordFactory()
        
        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            for key, value in self.context.items():
                setattr(record, key, value)
            return record
        
        logging.setLogRecordFactory(record_factory)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        logging.setLogRecordFactory(self.old_factory)


def log_function_call(logger: Optional[logging.Logger] = None):
    """Decorator to log function calls with arguments and timing."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_logger = logger or get_logger(func.__module__)
            
            # Log function entry
            func_logger.debug(
                f"Calling {func.__name__} with args={args}, kwargs={kwargs}"
            )
            
            start_time = datetime.now()
            try:
                result = func(*args, **kwargs)
                elapsed = (datetime.now() - start_time).total_seconds()
                
                func_logger.debug(
                    f"{func.__name__} completed successfully in {elapsed:.3f}s"
                )
                return result
            
            except Exception as e:
                elapsed = (datetime.now() - start_time).total_seconds()
                func_logger.error(
                    f"{func.__name__} failed after {elapsed:.3f}s: {e}"
                )
                raise
        
        return wrapper
    return decorator


class LogCapture:
    """Utility for capturing log messages in tests."""
    
    def __init__(self, logger_name: str, level: str = "DEBUG"):
        self.logger_name = logger_name
        self.level = level
        self.records = []
        self.handler = None
    
    def __enter__(self):
        self.handler = logging.Handler()
        self.handler.emit = lambda record: self.records.append(record)
        
        logger = logging.getLogger(self.logger_name)
        logger.addHandler(self.handler)
        logger.setLevel(getattr(logging, self.level))
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.handler:
            logger = logging.getLogger(self.logger_name)
            logger.removeHandler(self.handler)
    
    def get_messages(self, level: Optional[str] = None) -> list:
        """Get captured log messages, optionally filtered by level."""
        if level:
            level_num = getattr(logging, level.upper())
            return [r.getMessage() for r in self.records if r.levelno >= level_num]
        return [r.getMessage() for r in self.records]
    
    def clear(self):
        """Clear captured records."""
        self.records.clear()
