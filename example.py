#!/usr/bin/env python3
"""
Example usage of the refactored reCAPTCHA solver.

This script demonstrates how to use the new modular architecture
with various configuration options and error handling.
"""

import sys
from pathlib import Path

# Add the package to path for development
sys.path.insert(0, str(Path(__file__).parent))

from recaptcha_solver import <PERSON>captchaSolver, SolverConfig
from recaptcha_solver.config import BrowserConfig, ModelConfig, DetectionConfig
from recaptcha_solver.exceptions import RecaptchaSolverError


def example_basic_usage():
    """Basic usage example with default configuration."""
    print("=== Basic Usage Example ===")
    
    try:
        # Use default configuration
        with RecaptchaSolver() as solver:
            result = solver.solve("https://www.google.com/recaptcha/api2/demo")
            
            if result.success:
                print(f"✓ Success! Token: {result.recaptcha_token[:20]}...")
                print(f"⏱ Time taken: {result.total_time:.2f} seconds")
            else:
                print(f"✗ Failed: {result.error}")
    
    except RecaptchaSolverError as e:
        print(f"Solver error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")


def example_custom_configuration():
    """Example with custom configuration."""
    print("\n=== Custom Configuration Example ===")
    
    try:
        # Create custom configuration
        config = SolverConfig(
            browser=BrowserConfig(
                browser_type="chrome",
                headless=True,  # Run in headless mode
                window_width=1920,
                window_height=1080
            ),
            model=ModelConfig(
                confidence_threshold=0.3,  # Higher confidence threshold
                max_retries=5
            ),
            detection=DetectionConfig(
                enhance_contrast=True,
                max_detection_attempts=3
            ),
            log_level="DEBUG",  # Verbose logging
            max_solve_attempts=5
        )
        
        with RecaptchaSolver(config) as solver:
            result = solver.solve("https://www.google.com/recaptcha/api2/demo")
            
            print(f"Status: {result.status.value}")
            print(f"Success: {result.success}")
            print(f"Total attempts: {result.total_attempts}")
            print(f"Time taken: {result.total_time:.2f}s")
            
            if result.challenges:
                print(f"Success rate: {result.get_success_rate():.1%}")
    
    except Exception as e:
        print(f"Error: {e}")


def example_with_proxy():
    """Example using proxy configuration."""
    print("\n=== Proxy Configuration Example ===")
    
    try:
        config = SolverConfig(
            browser=BrowserConfig(
                browser_type="firefox",
                proxy_host="127.0.0.1",
                proxy_port=8080,
                # proxy_username="user",  # Optional
                # proxy_password="pass",  # Optional
            )
        )
        
        with RecaptchaSolver(config) as solver:
            # Test configuration first
            test_results = solver.test_configuration()
            print("Configuration test results:")
            for key, value in test_results.items():
                print(f"  {key}: {value}")
            
            if test_results.get("browser_available"):
                result = solver.solve("https://www.google.com/recaptcha/api2/demo")
                print(f"Solve result: {result.success}")
    
    except Exception as e:
        print(f"Proxy example error: {e}")


def example_configuration_from_file():
    """Example loading configuration from file."""
    print("\n=== Configuration from File Example ===")
    
    try:
        # Create a sample configuration file
        config = SolverConfig.create_for_development()
        config_file = Path("example_config.yaml")
        config.save_to_file(config_file, format="yaml")
        print(f"Saved configuration to: {config_file}")
        
        # Load configuration from file
        loaded_config = SolverConfig.from_file(config_file)
        print("Loaded configuration from file")
        
        with RecaptchaSolver(loaded_config) as solver:
            version_info = solver.get_version_info()
            print("Version info:")
            for key, value in version_info.items():
                print(f"  {key}: {value}")
        
        # Clean up
        config_file.unlink(missing_ok=True)
    
    except Exception as e:
        print(f"File configuration error: {e}")


def example_error_handling():
    """Example demonstrating error handling."""
    print("\n=== Error Handling Example ===")
    
    try:
        # Create configuration with invalid model path
        config = SolverConfig(
            model=ModelConfig(
                primary_model_path=Path("./nonexistent_model.onnx")
            )
        )
        
        with RecaptchaSolver(config) as solver:
            test_results = solver.test_configuration()
            
            if test_results["errors"]:
                print("Configuration errors detected:")
                for error in test_results["errors"]:
                    print(f"  - {error}")
            else:
                print("No configuration errors")
    
    except Exception as e:
        print(f"Expected error: {e}")


def example_performance_monitoring():
    """Example showing performance monitoring features."""
    print("\n=== Performance Monitoring Example ===")
    
    try:
        config = SolverConfig(
            enable_monitoring=True,
            log_level="INFO"
        )
        
        with RecaptchaSolver(config) as solver:
            # Simulate multiple solve attempts for metrics
            results = []
            
            for i in range(3):
                print(f"Attempt {i + 1}/3...")
                result = solver.solve("https://www.google.com/recaptcha/api2/demo")
                results.append(result)
            
            # Analyze results
            successful_results = [r for r in results if r.success]
            
            if successful_results:
                avg_time = sum(r.total_time for r in successful_results) / len(successful_results)
                print(f"Average solve time: {avg_time:.2f}s")
                print(f"Success rate: {len(successful_results)}/{len(results)}")
            else:
                print("No successful solves")
    
    except Exception as e:
        print(f"Performance monitoring error: {e}")


def main():
    """Run all examples."""
    print("reCAPTCHA Solver - Refactored Architecture Examples")
    print("=" * 60)
    
    # Run examples
    example_basic_usage()
    example_custom_configuration()
    example_with_proxy()
    example_configuration_from_file()
    example_error_handling()
    example_performance_monitoring()
    
    print("\n" + "=" * 60)
    print("Examples completed!")


if __name__ == "__main__":
    main()
