"""
Image validation utilities.
"""

import logging
from pathlib import Path
from typing import Optional, <PERSON>ple
import cv2
import numpy as np
from PIL import Image

from ..exceptions.image import ImageValidationError, InvalidImageFormatError


class ImageValidator:
    """Validates image files and properties."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        self.min_size = (10, 10)  # Minimum width, height
        self.max_size = (4000, 4000)  # Maximum width, height
        self.max_file_size = 50 * 1024 * 1024  # 50MB
    
    def validate_image_file(self, image_path: Path) -> bool:
        """
        Validate that a file is a valid image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            True if valid, False otherwise
            
        Raises:
            ImageValidationError: If validation fails with specific reason
        """
        try:
            # Check if file exists
            if not image_path.exists():
                raise ImageValidationError(
                    image_path=str(image_path),
                    validation_rule="file_exists"
                )
            
            # Check file size
            file_size = image_path.stat().st_size
            if file_size == 0:
                raise ImageValidationError(
                    image_path=str(image_path),
                    validation_rule="non_empty_file"
                )
            
            if file_size > self.max_file_size:
                raise ImageValidationError(
                    image_path=str(image_path),
                    validation_rule=f"file_size_under_{self.max_file_size}_bytes"
                )
            
            # Check file extension
            if image_path.suffix.lower() not in self.supported_formats:
                raise InvalidImageFormatError(
                    image_path=str(image_path),
                    expected_formats=self.supported_formats
                )
            
            # Try to open and validate with PIL
            try:
                with Image.open(image_path) as img:
                    img.verify()
                    
                    # Re-open to get dimensions (verify() closes the image)
                    with Image.open(image_path) as img:
                        width, height = img.size
                        
                        # Check dimensions
                        if width < self.min_size[0] or height < self.min_size[1]:
                            raise ImageValidationError(
                                image_path=str(image_path),
                                validation_rule=f"min_size_{self.min_size[0]}x{self.min_size[1]}"
                            )
                        
                        if width > self.max_size[0] or height > self.max_size[1]:
                            raise ImageValidationError(
                                image_path=str(image_path),
                                validation_rule=f"max_size_{self.max_size[0]}x{self.max_size[1]}"
                            )
                        
                        # Check if image has valid mode
                        if img.mode not in ['RGB', 'RGBA', 'L', 'P']:
                            self.logger.warning(f"Unusual image mode: {img.mode}")
            
            except Exception as e:
                if isinstance(e, (ImageValidationError, InvalidImageFormatError)):
                    raise
                raise ImageValidationError(
                    image_path=str(image_path),
                    validation_rule="pil_validation"
                )
            
            # Additional validation with OpenCV
            try:
                image = cv2.imread(str(image_path))
                if image is None:
                    raise ImageValidationError(
                        image_path=str(image_path),
                        validation_rule="opencv_readable"
                    )
                
                # Check if image has valid shape
                if len(image.shape) not in [2, 3]:
                    raise ImageValidationError(
                        image_path=str(image_path),
                        validation_rule="valid_image_shape"
                    )
            
            except Exception as e:
                if isinstance(e, ImageValidationError):
                    raise
                self.logger.warning(f"OpenCV validation failed for {image_path}: {e}")
            
            self.logger.debug(f"Image validation passed: {image_path}")
            return True
        
        except (ImageValidationError, InvalidImageFormatError):
            raise
        except Exception as e:
            raise ImageValidationError(
                image_path=str(image_path),
                validation_rule="general_validation"
            )
    
    def get_image_info(self, image_path: Path) -> Optional[dict]:
        """
        Get detailed information about an image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Dictionary with image information or None if failed
        """
        try:
            if not self.validate_image_file(image_path):
                return None
            
            with Image.open(image_path) as img:
                info = {
                    'path': str(image_path),
                    'format': img.format,
                    'mode': img.mode,
                    'size': img.size,
                    'width': img.width,
                    'height': img.height,
                    'file_size': image_path.stat().st_size,
                }
                
                # Add additional info if available
                if hasattr(img, 'info'):
                    info['metadata'] = img.info
                
                return info
        
        except Exception as e:
            self.logger.error(f"Failed to get image info for {image_path}: {e}")
            return None
    
    def validate_image_dimensions(self, image_path: Path, expected_ratio: Optional[float] = None) -> bool:
        """
        Validate image dimensions and aspect ratio.
        
        Args:
            image_path: Path to the image file
            expected_ratio: Expected aspect ratio (width/height)
            
        Returns:
            True if dimensions are valid
        """
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                
                # Check basic dimensions
                if width < self.min_size[0] or height < self.min_size[1]:
                    return False
                
                if width > self.max_size[0] or height > self.max_size[1]:
                    return False
                
                # Check aspect ratio if specified
                if expected_ratio is not None:
                    actual_ratio = width / height
                    ratio_tolerance = 0.1  # 10% tolerance
                    
                    if abs(actual_ratio - expected_ratio) > ratio_tolerance:
                        self.logger.warning(
                            f"Aspect ratio mismatch: expected {expected_ratio:.2f}, "
                            f"got {actual_ratio:.2f}"
                        )
                        return False
                
                return True
        
        except Exception as e:
            self.logger.error(f"Failed to validate dimensions for {image_path}: {e}")
            return False
    
    def is_image_corrupted(self, image_path: Path) -> bool:
        """
        Check if an image file is corrupted.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            True if image appears to be corrupted
        """
        try:
            # Try multiple validation methods
            
            # PIL validation
            with Image.open(image_path) as img:
                img.verify()
            
            # OpenCV validation
            image = cv2.imread(str(image_path))
            if image is None:
                return True
            
            # Check for valid pixel data
            if np.all(image == 0) or np.all(image == 255):
                self.logger.warning(f"Image appears to be all black or all white: {image_path}")
                return True
            
            return False
        
        except Exception as e:
            self.logger.warning(f"Image corruption check failed for {image_path}: {e}")
            return True
    
    def get_image_hash(self, image_path: Path) -> Optional[str]:
        """
        Calculate a perceptual hash of the image for duplicate detection.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Hash string or None if failed
        """
        try:
            import hashlib
            
            # Simple file hash for now
            with open(image_path, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()
            
            return file_hash
        
        except Exception as e:
            self.logger.error(f"Failed to calculate hash for {image_path}: {e}")
            return None
