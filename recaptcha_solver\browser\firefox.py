"""
Firefox browser implementation.
"""

from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from seleniumwire import webdriver

from .base import BaseBrowser


class FirefoxBrowser(BaseBrowser):
    """Firefox browser implementation."""
    
    def get_browser_name(self) -> str:
        """Get the browser name."""
        return "Firefox"
    
    def _create_driver(self) -> webdriver.Firefox:
        """Create Firefox driver with configuration."""
        options = self._create_firefox_options()
        service = self._create_firefox_service()
        seleniumwire_options = self._create_seleniumwire_options()
        
        return webdriver.Firefox(
            service=service,
            options=options,
            seleniumwire_options=seleniumwire_options
        )
    
    def _create_firefox_options(self) -> FirefoxOptions:
        """Create Firefox options based on configuration."""
        options = FirefoxOptions()
        
        # Set binary path if specified
        if self.config.binary_path:
            options.binary_location = str(self.config.binary_path)
        
        # Basic options
        if self.config.headless:
            options.add_argument("--headless")
        
        # Set preferences from config
        for pref_name, pref_value in self.config.firefox_prefs.items():
            options.set_preference(pref_name, pref_value)
        
        # Language preference
        options.set_preference('intl.accept_languages', 'en-US')
        
        # Image loading
        if self.config.disable_images:
            options.set_preference('permissions.default.image', 2)
        else:
            options.set_preference('permissions.default.image', 1)
        
        # JavaScript
        if self.config.disable_javascript:
            options.set_preference('javascript.enabled', False)
        
        # Notifications
        options.set_preference('dom.webnotifications.enabled', False)
        
        # Security settings
        if self.config.ignore_ssl_errors:
            options.set_preference('security.tls.insecure_fallback_hosts', 'localhost')
            options.set_preference('security.tls.unrestricted_rc4_fallback', True)
        
        # User agent
        user_agent = self.config.get_effective_user_agent()
        options.set_preference('general.useragent.override', user_agent)
        
        return options
    
    def _create_firefox_service(self) -> FirefoxService:
        """Create Firefox service."""
        return FirefoxService()
    
    def _create_seleniumwire_options(self) -> dict:
        """Create selenium-wire options."""
        seleniumwire_options = {
            'verify_ssl': not self.config.ignore_ssl_errors,
            'disable_encoding': True,
            'suppress_connection_errors': True,
            'request_storage_base_dir': None  # Use memory storage
        }
        
        # Add proxy if configured
        if self.config.proxy_host and self.config.proxy_port:
            proxy_url = self.config.proxy_url
            seleniumwire_options['proxy'] = {
                'http': proxy_url,
                'https': proxy_url,
                'no_proxy': 'localhost,127.0.0.1'
            }
        
        return seleniumwire_options
