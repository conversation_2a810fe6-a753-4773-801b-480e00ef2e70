"""
Base exception classes for the reCAPTCHA solver.
"""

from typing import Optional, Dict, Any


class RecaptchaSolverError(Exception):
    """Base exception for all reCAPTCHA solver errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.cause = cause
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for serialization."""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "cause": str(self.cause) if self.cause else None
        }
    
    def __str__(self) -> str:
        base_msg = f"[{self.error_code}] {self.message}"
        if self.details:
            details_str = ", ".join(f"{k}={v}" for k, v in self.details.items())
            base_msg += f" (Details: {details_str})"
        if self.cause:
            base_msg += f" (Caused by: {self.cause})"
        return base_msg


class RetryableError(RecaptchaSolverError):
    """Base class for errors that can be retried."""
    
    def __init__(
        self, 
        message: str, 
        retry_after: Optional[float] = None,
        max_retries: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after
        self.max_retries = max_retries


class NonRetryableError(RecaptchaSolverError):
    """Base class for errors that should not be retried."""
    pass
