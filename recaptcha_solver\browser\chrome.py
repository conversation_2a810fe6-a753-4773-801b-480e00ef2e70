"""
Chrome browser implementation.
"""

import os
from pathlib import Path
from typing import Optional
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from seleniumwire import webdriver

from .base import BaseBrowser
from ..config.browser_config import BrowserConfig
from ..exceptions.browser import BrowserNotFoundError


class ChromeBrowser(BaseBrowser):
    """Chrome browser implementation."""
    
    def get_browser_name(self) -> str:
        """Get the browser name."""
        return "Chrome"
    
    def _create_driver(self) -> webdriver.Chrome:
        """Create Chrome driver with configuration."""
        options = self._create_chrome_options()
        service = self._create_chrome_service()
        seleniumwire_options = self._create_seleniumwire_options()
        
        return webdriver.Chrome(
            service=service,
            options=options,
            seleniumwire_options=seleniumwire_options
        )
    
    def _create_chrome_options(self) -> ChromeOptions:
        """Create Chrome options based on configuration."""
        options = ChromeOptions()
        
        # Set binary path if specified
        if self.config.binary_path:
            options.binary_location = str(self.config.binary_path)
        else:
            # Try to find Chrome binary automatically
            binary_path = self._find_chrome_binary()
            if binary_path:
                options.binary_location = str(binary_path)
        
        # Basic options
        if self.config.headless:
            options.add_argument("--headless")
        
        # Security options
        if self.config.ignore_ssl_errors:
            options.add_argument("--ignore-certificate-errors")
            options.add_argument("--ignore-certificate-errors-spki-list")
            options.add_argument("--ignore-ssl-errors")
            options.add_argument("--allow-insecure-localhost")
            options.add_argument("--accept-insecure-certs")
        
        if self.config.allow_insecure_content:
            options.add_argument("--allow-running-insecure-content")
        
        if self.config.disable_web_security:
            options.add_argument("--disable-web-security")
        
        # Performance options
        if self.config.disable_images:
            prefs = {"profile.managed_default_content_settings.images": 2}
            options.add_experimental_option("prefs", prefs)
        
        if self.config.disable_javascript:
            prefs = {"profile.managed_default_content_settings.javascript": 2}
            options.add_experimental_option("prefs", prefs)
        
        # Window size
        if not self.config.headless:
            options.add_argument(f"--window-size={self.config.window_width},{self.config.window_height}")
        
        # User agent
        user_agent = self.config.get_effective_user_agent()
        options.add_argument(f"--user-agent={user_agent}")
        
        # Add custom Chrome options
        for option in self.config.chrome_options:
            options.add_argument(option)
        
        # Additional stability options
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-popup-blocking")
        options.add_argument("--disable-notifications")
        options.add_argument("--lang=en-US")
        
        if self.config.disable_extensions:
            options.add_argument("--disable-extensions")
        
        # Enable image loading for reCAPTCHA
        options.add_argument("--blink-settings=imagesEnabled=true")
        
        return options
    
    def _create_chrome_service(self) -> ChromeService:
        """Create Chrome service."""
        return ChromeService()
    
    def _create_seleniumwire_options(self) -> dict:
        """Create selenium-wire options."""
        seleniumwire_options = {
            'verify_ssl': self.config.ignore_ssl_errors,
            'disable_encoding': True,
            'suppress_connection_errors': True,
            'request_storage_base_dir': None  # Use memory storage
        }
        
        # Add proxy if configured
        if self.config.proxy_host and self.config.proxy_port:
            proxy_url = self.config.proxy_url
            seleniumwire_options['proxy'] = {
                'http': proxy_url,
                'https': proxy_url,
                'no_proxy': 'localhost,127.0.0.1'
            }
        
        return seleniumwire_options
    
    def _find_chrome_binary(self) -> Optional[Path]:
        """Find Chrome binary automatically on Windows."""
        possible_paths = [
            r'C:\Program Files\Google\Chrome\Application\chrome.exe',
            r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
            r'C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe'.format(
                os.getenv('USERNAME', 'Admin')
            ),
            r'C:\Users\<USER>\AppData\Local\Google\Chrome SxS\Application\chrome.exe'.format(
                os.getenv('USERNAME', 'Admin')
            ),
        ]
        
        for path_str in possible_paths:
            path = Path(path_str)
            if path.exists():
                self.logger.info(f"Found Chrome binary at: {path}")
                return path
        
        self.logger.warning("Chrome binary not found in common locations")
        return None
    
    def _configure_driver(self) -> None:
        """Configure Chrome-specific driver settings."""
        super()._configure_driver()
        
        if not self.driver:
            return
        
        # Set Chrome-specific scopes for selenium-wire
        if hasattr(self.driver, 'scopes'):
            self.driver.scopes = ['.*google.com/recaptcha.*']
