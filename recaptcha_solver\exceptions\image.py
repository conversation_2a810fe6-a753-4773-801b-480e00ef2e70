"""
Image-related exception classes.
"""

from typing import Optional
from .base import RecaptchaSolverError, RetryableError, NonRetryableError


class ImageError(RecaptchaSolverError):
    """Base class for image-related errors."""
    pass


class ImageDownloadError(ImageError, RetryableError):
    """Raised when image download fails."""
    
    def __init__(
        self, 
        url: str, 
        status_code: Optional[int] = None,
        reason: Optional[str] = None,
        **kwargs
    ):
        message = f"Failed to download image from: {url}"
        if status_code:
            message += f" (HTTP {status_code})"
        if reason:
            message += f" - {reason}"
        
        super().__init__(
            message,
            details={
                "url": url,
                "status_code": status_code,
                "reason": reason
            },
            **kwargs
        )


class ImageProcessingError(ImageError, RetryableError):
    """Raised when image processing fails."""
    
    def __init__(
        self, 
        operation: str, 
        image_path: Optional[str] = None,
        reason: Optional[str] = None,
        **kwargs
    ):
        message = f"Image processing failed: {operation}"
        if image_path:
            message += f" (file: {image_path})"
        if reason:
            message += f" - {reason}"
        
        super().__init__(
            message,
            details={
                "operation": operation,
                "image_path": image_path,
                "reason": reason
            },
            **kwargs
        )


class InvalidImageFormatError(ImageError, NonRetryableError):
    """Raised when image format is invalid or unsupported."""
    
    def __init__(
        self, 
        image_path: str, 
        expected_formats: Optional[list] = None,
        **kwargs
    ):
        message = f"Invalid or unsupported image format: {image_path}"
        if expected_formats:
            message += f". Expected formats: {', '.join(expected_formats)}"
        
        super().__init__(
            message,
            details={
                "image_path": image_path,
                "expected_formats": expected_formats or []
            },
            **kwargs
        )


class ImageValidationError(ImageError, NonRetryableError):
    """Raised when image validation fails."""
    
    def __init__(
        self, 
        image_path: str, 
        validation_rule: str,
        **kwargs
    ):
        message = f"Image validation failed: {image_path} (rule: {validation_rule})"
        super().__init__(
            message,
            details={
                "image_path": image_path,
                "validation_rule": validation_rule
            },
            **kwargs
        )
