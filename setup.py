#!/usr/bin/env python3
"""
Setup script for the reCAPTCHA Solver package.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [
            line.strip() 
            for line in f 
            if line.strip() and not line.startswith('#')
        ]
else:
    requirements = [
        "selenium>=4.15.0",
        "selenium-wire>=5.1.0", 
        "ultralytics>=8.0.0",
        "opencv-python>=4.8.0",
        "numpy>=1.24.0",
        "Pillow>=10.0.0",
        "requests>=2.31.0",
        "webdriver-manager>=4.0.0",
        "urllib3>=2.0.0",
        "PyYAML>=6.0.0",
        "colorama>=0.4.6"
    ]

# Development requirements
dev_requirements = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0"
]

setup(
    name="recaptcha-solver",
    version="2.0.0",
    author="reCAPTCHA Solver Team",
    author_email="<EMAIL>",
    description="Professional-grade reCAPTCHA solver using computer vision and machine learning",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/recaptcha-solver",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": dev_requirements,
        "all": requirements + dev_requirements,
    },
    entry_points={
        "console_scripts": [
            "recaptcha-solver=cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "recaptcha_solver": [
            "models/*.onnx",
            "config/*.yaml",
            "config/*.json",
        ],
    },
    keywords=[
        "recaptcha", "solver", "automation", "computer-vision", 
        "machine-learning", "yolo", "selenium", "browser-automation"
    ],
    project_urls={
        "Bug Reports": "https://github.com/your-username/recaptcha-solver/issues",
        "Source": "https://github.com/your-username/recaptcha-solver",
        "Documentation": "https://github.com/your-username/recaptcha-solver/blob/main/README.md",
    },
)
