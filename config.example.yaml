# Example configuration file for reCAPTCHA Solver
# Copy this file to config.yaml and modify as needed

# Browser configuration
browser:
  browser_type: chrome          # chrome, firefox, edge
  headless: false               # Run in headless mode
  window_width: 1280
  window_height: 1024
  
  # Proxy settings (optional)
  # proxy_host: 127.0.0.1
  # proxy_port: 8080
  # proxy_username: user
  # proxy_password: pass
  
  # Custom browser binary path (optional)
  # binary_path: /path/to/browser
  
  # Timeouts (in seconds)
  page_load_timeout: 45.0
  implicit_wait: 10.0
  element_wait_timeout: 15.0
  
  # Security settings
  ignore_ssl_errors: true
  allow_insecure_content: true
  disable_web_security: true
  
  # Performance settings
  disable_images: false
  disable_javascript: false
  disable_extensions: true
  
  # Custom user agent (optional)
  # user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

# Model configuration
model:
  primary_model_path: ./models/models.onnx
  
  # Fallback models (optional)
  # fallback_model_paths:
  #   - ./models/backup_model.onnx
  
  # Detection settings
  confidence_threshold: 0.25
  iou_threshold: 0.1
  max_detections: 100
  
  # Performance settings
  use_gpu: true
  batch_size: 1
  # num_threads: 4
  
  # Retry settings
  max_retries: 3
  retry_delay: 1.0
  
  # Timeouts
  load_timeout: 30.0
  prediction_timeout: 10.0

# Detection configuration
detection:
  # Grid settings
  default_grid_size: 3
  supported_grid_sizes: [3, 4]
  
  # Detection thresholds
  min_confidence: 0.25
  iou_threshold: 0.1
  overlap_threshold: 0.3
  
  # Image processing
  enhance_contrast: true
  apply_noise_reduction: true
  resize_images: false
  target_image_size: [640, 640]
  
  # Retry settings
  max_detection_attempts: 3
  retry_on_no_detection: true
  retry_delay: 0.5
  
  # Timeouts
  detection_timeout: 30.0
  image_download_timeout: 15.0
  
  # Performance settings
  parallel_processing: false
  max_workers: 4

# General solver settings
max_solve_attempts: 10
attempt_delay: 2.0

# Logging configuration
log_level: INFO                 # DEBUG, INFO, WARNING, ERROR, CRITICAL
# log_file: ./logs/solver.log   # Optional log file
log_format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
enable_file_logging: true
enable_console_logging: true

# Performance settings
cleanup_temp_files: true
# temp_dir: ./temp              # Optional temp directory

# Monitoring settings
enable_monitoring: true
# metrics_file: ./metrics.json  # Optional metrics file

# Security settings
verify_ssl: false
allow_redirects: true

# API settings (for future use)
api_enabled: false
api_host: localhost
api_port: 8080
api_workers: 1

# Rate limiting
rate_limit_enabled: false
requests_per_minute: 60
