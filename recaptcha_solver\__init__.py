"""
reCAPTCHA Solver - A professional-grade reCAPTCHA solving library.

This package provides a comprehensive solution for solving reCAPTCHA challenges
using advanced computer vision and machine learning techniques.
"""

__version__ = "2.0.0"
__author__ = "reCAPTCHA Solver Team"
__email__ = "<EMAIL>"

from .core.solver import RecaptchaSolver
from .core.result import SolverResult
from .config.settings import SolverConfig
from .exceptions.base import RecaptchaSolverError

__all__ = [
    "RecaptchaSolver",
    "SolverResult", 
    "SolverConfig",
    "RecaptchaSolverError",
]
