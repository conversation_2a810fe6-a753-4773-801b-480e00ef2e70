"""
Configuration-related exception classes.
"""

from typing import Optional, Any
from .base import RecaptchaSolverError, NonRetryableError


class ConfigError(RecaptchaSolverError):
    """Base class for configuration-related errors."""
    pass


class InvalidConfigError(ConfigError, NonRetryableError):
    """Raised when configuration is invalid."""
    
    def __init__(
        self, 
        config_key: str, 
        config_value: Any,
        expected_type: Optional[str] = None,
        valid_values: Optional[list] = None,
        **kwargs
    ):
        message = f"Invalid configuration value for '{config_key}': {config_value}"
        
        if expected_type:
            message += f" (expected type: {expected_type})"
        
        if valid_values:
            message += f" (valid values: {', '.join(map(str, valid_values))})"
        
        super().__init__(
            message,
            details={
                "config_key": config_key,
                "config_value": config_value,
                "expected_type": expected_type,
                "valid_values": valid_values
            },
            **kwargs
        )


class MissingConfigError(ConfigError, NonRetryableError):
    """Raised when required configuration is missing."""
    
    def __init__(
        self, 
        config_key: str,
        **kwargs
    ):
        message = f"Missing required configuration: {config_key}"
        super().__init__(
            message,
            details={"config_key": config_key},
            **kwargs
        )


class ConfigFileError(ConfigError, NonRetryableError):
    """Raised when configuration file cannot be loaded."""
    
    def __init__(
        self, 
        config_file: str, 
        reason: Optional[str] = None,
        **kwargs
    ):
        message = f"Failed to load configuration file: {config_file}"
        if reason:
            message += f" ({reason})"
        
        super().__init__(
            message,
            details={
                "config_file": config_file,
                "reason": reason
            },
            **kwargs
        )
