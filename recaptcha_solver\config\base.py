"""
Base configuration classes and utilities.
"""

import os
import json
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Type, TypeVar, Union
from pathlib import Path
from dataclasses import dataclass, fields, is_dataclass
from ..exceptions.config import ConfigError, InvalidConfigError, ConfigFileError

try:
    import yaml
    HAS_YAML = True
except ImportError:
    HAS_YAML = False

T = TypeVar('T', bound='BaseConfig')


class ConfigValidator:
    """Utility class for configuration validation."""
    
    @staticmethod
    def validate_type(value: Any, expected_type: Type, field_name: str) -> Any:
        """Validate that value matches expected type."""
        if not isinstance(value, expected_type):
            raise InvalidConfigError(
                config_key=field_name,
                config_value=value,
                expected_type=expected_type.__name__
            )
        return value
    
    @staticmethod
    def validate_choices(value: Any, choices: list, field_name: str) -> Any:
        """Validate that value is in allowed choices."""
        if value not in choices:
            raise InvalidConfigError(
                config_key=field_name,
                config_value=value,
                valid_values=choices
            )
        return value
    
    @staticmethod
    def validate_range(value: Union[int, float], min_val: Optional[Union[int, float]], 
                      max_val: Optional[Union[int, float]], field_name: str) -> Union[int, float]:
        """Validate that numeric value is within range."""
        if min_val is not None and value < min_val:
            raise InvalidConfigError(
                config_key=field_name,
                config_value=value,
                expected_type=f"value >= {min_val}"
            )
        if max_val is not None and value > max_val:
            raise InvalidConfigError(
                config_key=field_name,
                config_value=value,
                expected_type=f"value <= {max_val}"
            )
        return value
    
    @staticmethod
    def validate_path(path: Union[str, Path], must_exist: bool, field_name: str) -> Path:
        """Validate file/directory path."""
        path_obj = Path(path)
        if must_exist and not path_obj.exists():
            raise InvalidConfigError(
                config_key=field_name,
                config_value=str(path),
                expected_type="existing path"
            )
        return path_obj


@dataclass
class BaseConfig(ABC):
    """Base class for all configuration classes."""
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        self.validate()
    
    @abstractmethod
    def validate(self) -> None:
        """Validate the configuration. Must be implemented by subclasses."""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        if is_dataclass(self):
            result = {}
            for field in fields(self):
                value = getattr(self, field.name)
                if isinstance(value, BaseConfig):
                    result[field.name] = value.to_dict()
                elif isinstance(value, Path):
                    result[field.name] = str(value)
                else:
                    result[field.name] = value
            return result
        return {}
    
    @classmethod
    def from_dict(cls: Type[T], data: Dict[str, Any]) -> T:
        """Create configuration from dictionary."""
        # Filter data to only include fields that exist in the dataclass
        if is_dataclass(cls):
            field_names = {f.name for f in fields(cls)}
            filtered_data = {k: v for k, v in data.items() if k in field_names}
            return cls(**filtered_data)
        return cls()
    
    @classmethod
    def from_file(cls: Type[T], file_path: Union[str, Path]) -> T:
        """Load configuration from file (JSON or YAML)."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise ConfigFileError(str(file_path), "File does not exist")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() in ['.yml', '.yaml']:
                    if not HAS_YAML:
                        raise ConfigFileError(str(file_path), "PyYAML not installed")
                    data = yaml.safe_load(f)
                elif file_path.suffix.lower() == '.json':
                    data = json.load(f)
                else:
                    raise ConfigFileError(str(file_path), "Unsupported file format")

            return cls.from_dict(data)

        except json.JSONDecodeError as e:
            raise ConfigFileError(str(file_path), f"JSON parse error: {e}")
        except Exception as e:
            if HAS_YAML and "yaml" in str(e).lower():
                raise ConfigFileError(str(file_path), f"YAML parse error: {e}")
            raise ConfigFileError(str(file_path), str(e))
    
    def save_to_file(self, file_path: Union[str, Path], format: str = 'yaml') -> None:
        """Save configuration to file."""
        file_path = Path(file_path)
        data = self.to_dict()
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                if format.lower() in ['yml', 'yaml']:
                    if not HAS_YAML:
                        raise ValueError("PyYAML not installed")
                    yaml.dump(data, f, default_flow_style=False, indent=2)
                elif format.lower() == 'json':
                    json.dump(data, f, indent=2)
                else:
                    raise ValueError(f"Unsupported format: {format}")

        except Exception as e:
            raise ConfigFileError(str(file_path), f"Save error: {e}")
    
    @classmethod
    def from_env(cls: Type[T], prefix: str = "") -> T:
        """Create configuration from environment variables."""
        data = {}
        
        if is_dataclass(cls):
            for field in fields(cls):
                env_key = f"{prefix}{field.name}".upper()
                env_value = os.getenv(env_key)
                
                if env_value is not None:
                    # Convert string to appropriate type
                    if field.type == bool:
                        data[field.name] = env_value.lower() in ('true', '1', 'yes', 'on')
                    elif field.type == int:
                        data[field.name] = int(env_value)
                    elif field.type == float:
                        data[field.name] = float(env_value)
                    else:
                        data[field.name] = env_value
        
        return cls.from_dict(data)
    
    def merge(self: T, other: T) -> T:
        """Merge this configuration with another, other takes precedence."""
        if not isinstance(other, self.__class__):
            raise ValueError("Can only merge with same configuration type")
        
        self_dict = self.to_dict()
        other_dict = other.to_dict()
        
        # Deep merge dictionaries
        merged = self._deep_merge(self_dict, other_dict)
        return self.__class__.from_dict(merged)
    
    @staticmethod
    def _deep_merge(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries."""
        result = dict1.copy()
        
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = BaseConfig._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
