"""
Browser configuration settings.
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, List
from pathlib import Path
from .base import BaseConfig, ConfigValidator


@dataclass
class BrowserConfig(BaseConfig):
    """Configuration for browser settings."""
    
    # Browser selection
    browser_type: str = "chrome"
    binary_path: Optional[Path] = None
    
    # Timeouts (in seconds)
    page_load_timeout: float = 45.0
    implicit_wait: float = 10.0
    element_wait_timeout: float = 15.0
    
    # Window settings
    window_width: int = 1280
    window_height: int = 1024
    headless: bool = False
    
    # Performance settings
    disable_images: bool = False
    disable_javascript: bool = False
    disable_plugins: bool = True
    disable_extensions: bool = True
    
    # Security settings
    ignore_ssl_errors: bool = True
    allow_insecure_content: bool = True
    disable_web_security: bool = True
    
    # Proxy settings
    proxy_host: Optional[str] = None
    proxy_port: Optional[int] = None
    proxy_username: Optional[str] = None
    proxy_password: Optional[str] = None
    
    # User agent
    user_agent: Optional[str] = None
    
    # Additional Chrome options
    chrome_options: List[str] = field(default_factory=lambda: [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-popup-blocking",
        "--disable-notifications",
        "--lang=en-US"
    ])
    
    # Additional Firefox preferences
    firefox_prefs: Dict[str, any] = field(default_factory=lambda: {
        "intl.accept_languages": "en-US",
        "permissions.default.image": 1,
        "dom.webnotifications.enabled": False
    })
    
    # Additional Edge options
    edge_options: List[str] = field(default_factory=lambda: [
        "--no-sandbox",
        "--disable-gpu",
        "--lang=en-US"
    ])
    
    def validate(self) -> None:
        """Validate browser configuration."""
        # Validate browser type
        valid_browsers = ["chrome", "firefox", "edge"]
        ConfigValidator.validate_choices(
            self.browser_type, valid_browsers, "browser_type"
        )
        
        # Validate timeouts
        ConfigValidator.validate_range(
            self.page_load_timeout, 1.0, 300.0, "page_load_timeout"
        )
        ConfigValidator.validate_range(
            self.implicit_wait, 0.0, 60.0, "implicit_wait"
        )
        ConfigValidator.validate_range(
            self.element_wait_timeout, 1.0, 120.0, "element_wait_timeout"
        )
        
        # Validate window dimensions
        ConfigValidator.validate_range(
            self.window_width, 100, 4000, "window_width"
        )
        ConfigValidator.validate_range(
            self.window_height, 100, 4000, "window_height"
        )
        
        # Validate proxy settings
        if self.proxy_host and self.proxy_port:
            ConfigValidator.validate_range(
                self.proxy_port, 1, 65535, "proxy_port"
            )
        
        # Validate binary path if provided
        if self.binary_path:
            self.binary_path = ConfigValidator.validate_path(
                self.binary_path, must_exist=True, field_name="binary_path"
            )
    
    @property
    def proxy_url(self) -> Optional[str]:
        """Get proxy URL if proxy is configured."""
        if not self.proxy_host or not self.proxy_port:
            return None
        
        if self.proxy_username and self.proxy_password:
            return f"http://{self.proxy_username}:{self.proxy_password}@{self.proxy_host}:{self.proxy_port}"
        else:
            return f"http://{self.proxy_host}:{self.proxy_port}"
    
    def get_default_user_agent(self) -> str:
        """Get default user agent for the browser."""
        if self.browser_type == "chrome":
            return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        elif self.browser_type == "firefox":
            return "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0"
        elif self.browser_type == "edge":
            return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"
        else:
            return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    
    def get_effective_user_agent(self) -> str:
        """Get the user agent to use (custom or default)."""
        return self.user_agent or self.get_default_user_agent()
