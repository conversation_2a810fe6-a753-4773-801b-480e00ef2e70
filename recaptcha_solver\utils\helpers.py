"""
General utility functions and helpers.
"""

import os
import shutil
import hashlib
import tempfile
from pathlib import Path
from typing import List, Optional, Union, Dict, Any
import logging


def cleanup_files(pattern: str = "*.png", directory: Optional[Path] = None) -> int:
    """
    Clean up files matching the specified pattern.
    
    Args:
        pattern: File pattern to match (e.g., "*.png", "temp_*")
        directory: Directory to search in (default: current directory)
        
    Returns:
        Number of files deleted
    """
    logger = logging.getLogger(__name__)
    directory = directory or Path.cwd()
    
    try:
        files_to_delete = list(directory.glob(pattern))
        count = 0
        
        for file_path in files_to_delete:
            try:
                if file_path.is_file():
                    file_path.unlink()
                    count += 1
                    logger.debug(f"Deleted file: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to delete {file_path}: {e}")
        
        if count > 0:
            logger.info(f"Cleaned up {count} files matching '{pattern}'")
        
        return count
    
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        return 0


def ensure_directory(path: Union[str, Path], create_parents: bool = True) -> Path:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        path: Directory path
        create_parents: Whether to create parent directories
        
    Returns:
        Path object for the directory
    """
    path_obj = Path(path)
    
    if not path_obj.exists():
        path_obj.mkdir(parents=create_parents, exist_ok=True)
    elif not path_obj.is_dir():
        raise ValueError(f"Path exists but is not a directory: {path}")
    
    return path_obj


def calculate_file_hash(file_path: Path, algorithm: str = "md5") -> str:
    """
    Calculate hash of a file.
    
    Args:
        file_path: Path to the file
        algorithm: Hash algorithm (md5, sha1, sha256)
        
    Returns:
        Hex digest of the file hash
    """
    hash_func = hashlib.new(algorithm)
    
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_func.update(chunk)
    
    return hash_func.hexdigest()


def get_file_size_mb(file_path: Path) -> float:
    """Get file size in megabytes."""
    return file_path.stat().st_size / (1024 * 1024)


def create_temp_directory(prefix: str = "recaptcha_solver_") -> Path:
    """
    Create a temporary directory.
    
    Args:
        prefix: Prefix for the directory name
        
    Returns:
        Path to the created directory
    """
    temp_dir = Path(tempfile.mkdtemp(prefix=prefix))
    return temp_dir


def safe_remove_directory(directory: Path, ignore_errors: bool = True) -> bool:
    """
    Safely remove a directory and all its contents.
    
    Args:
        directory: Directory to remove
        ignore_errors: Whether to ignore errors during removal
        
    Returns:
        True if successful, False otherwise
    """
    logger = logging.getLogger(__name__)
    
    try:
        if directory.exists() and directory.is_dir():
            shutil.rmtree(directory, ignore_errors=ignore_errors)
            logger.debug(f"Removed directory: {directory}")
            return True
        return False
    
    except Exception as e:
        logger.warning(f"Failed to remove directory {directory}: {e}")
        return False


def get_available_memory_mb() -> Optional[float]:
    """
    Get available system memory in megabytes.
    
    Returns:
        Available memory in MB or None if unable to determine
    """
    try:
        import psutil
        memory = psutil.virtual_memory()
        return memory.available / (1024 * 1024)
    except ImportError:
        # Fallback for systems without psutil
        try:
            with open('/proc/meminfo', 'r') as f:
                for line in f:
                    if line.startswith('MemAvailable:'):
                        # Value is in kB
                        kb = int(line.split()[1])
                        return kb / 1024
        except (FileNotFoundError, ValueError, IndexError):
            pass
    
    return None


def format_bytes(bytes_value: int) -> str:
    """
    Format bytes into human-readable string.
    
    Args:
        bytes_value: Number of bytes
        
    Returns:
        Formatted string (e.g., "1.5 MB", "2.3 GB")
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f} PB"


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted string (e.g., "1m 30s", "2h 15m")
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    
    minutes = int(seconds // 60)
    remaining_seconds = seconds % 60
    
    if minutes < 60:
        return f"{minutes}m {remaining_seconds:.0f}s"
    
    hours = minutes // 60
    remaining_minutes = minutes % 60
    
    return f"{hours}h {remaining_minutes}m"


def validate_url(url: str) -> bool:
    """
    Validate if a string is a valid URL.
    
    Args:
        url: URL string to validate
        
    Returns:
        True if valid URL, False otherwise
    """
    try:
        from urllib.parse import urlparse
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def merge_dictionaries(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Deep merge two dictionaries.
    
    Args:
        dict1: First dictionary
        dict2: Second dictionary (takes precedence)
        
    Returns:
        Merged dictionary
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_dictionaries(result[key], value)
        else:
            result[key] = value
    
    return result


def find_executable(name: str, paths: Optional[List[str]] = None) -> Optional[Path]:
    """
    Find an executable in the system PATH or specified paths.
    
    Args:
        name: Executable name
        paths: Optional list of paths to search
        
    Returns:
        Path to executable or None if not found
    """
    # Check specified paths first
    if paths:
        for path_str in paths:
            path = Path(path_str)
            if path.is_file() and os.access(path, os.X_OK):
                return path
    
    # Check system PATH
    path_env = os.environ.get('PATH', '')
    for path_str in path_env.split(os.pathsep):
        if not path_str:
            continue
        
        path = Path(path_str) / name
        if path.is_file() and os.access(path, os.X_OK):
            return path
        
        # On Windows, also check with .exe extension
        if os.name == 'nt':
            exe_path = Path(path_str) / f"{name}.exe"
            if exe_path.is_file() and os.access(exe_path, os.X_OK):
                return exe_path
    
    return None


def get_system_info() -> Dict[str, Any]:
    """
    Get basic system information.
    
    Returns:
        Dictionary with system information
    """
    import platform
    
    info = {
        "platform": platform.platform(),
        "system": platform.system(),
        "release": platform.release(),
        "version": platform.version(),
        "machine": platform.machine(),
        "processor": platform.processor(),
        "python_version": platform.python_version(),
    }
    
    # Add memory info if available
    memory_mb = get_available_memory_mb()
    if memory_mb:
        info["available_memory_mb"] = memory_mb
    
    return info
