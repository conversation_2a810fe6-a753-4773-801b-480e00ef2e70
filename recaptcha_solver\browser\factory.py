"""
Browser factory for creating browser instances.
"""

from typing import Dict, Type
from ..config.browser_config import BrowserConfig
from ..exceptions.browser import BrowserNotFoundError
from .base import BaseBrowser
from .chrome import ChromeBrowser
from .firefox import FirefoxBrowser
from .edge import EdgeBrowser


class BrowserFactory:
    """Factory class for creating browser instances."""
    
    _browsers: Dict[str, Type[BaseBrowser]] = {
        "chrome": ChromeBrowser,
        "firefox": FirefoxBrowser,
        "edge": EdgeBrowser,
    }
    
    @classmethod
    def create_browser(cls, config: BrowserConfig) -> BaseBrowser:
        """Create a browser instance based on configuration."""
        browser_type = config.browser_type.lower()
        
        if browser_type not in cls._browsers:
            available_browsers = list(cls._browsers.keys())
            raise BrowserNotFoundError(
                browser_name=browser_type,
                available_browsers=available_browsers
            )
        
        browser_class = cls._browsers[browser_type]
        return browser_class(config)
    
    @classmethod
    def get_supported_browsers(cls) -> list:
        """Get list of supported browser types."""
        return list(cls._browsers.keys())
    
    @classmethod
    def register_browser(cls, name: str, browser_class: Type[BaseBrowser]) -> None:
        """Register a new browser type."""
        cls._browsers[name.lower()] = browser_class
    
    @classmethod
    def is_browser_supported(cls, browser_type: str) -> bool:
        """Check if a browser type is supported."""
        return browser_type.lower() in cls._browsers
